# 📊 美股ETF持仓分析工具 - 使用说明

## 🎯 功能介绍

这是一个**一键式美股ETF持仓分析工具**，支持：
- 🔍 **单个ETF查询**：输入ETF代码，获取前十大持仓
- 📊 **批量CSV处理**：处理包含多个ETF的CSV文件
- 🌍 **全球股票支持**：美股、国际股票（欧股、亚股等）
- 🔄 **多数据源**：Yahoo Finance、Vanguard、yfinance自动切换

## 🚀 一键使用方法

### 方法1：交互式使用（推荐新手）
```bash
python3 etf_analyzer.py
```
然后按提示选择功能即可！

### 方法2：命令行使用

#### 查询单个ETF
```bash
# 查询VOO（Vanguard S&P 500 ETF）
python3 etf_analyzer.py VOO

# 查询QQQ（Invesco QQQ Trust）
python3 etf_analyzer.py QQQ

# 指定数据源查询
python3 etf_analyzer.py VOO --source 1  # 使用Yahoo Finance
python3 etf_analyzer.py VOO --source 2  # 使用Vanguard Selenium
python3 etf_analyzer.py VOO --source 3  # 使用yfinance
```

#### 处理CSV文件
```bash
# 处理CSV文件（自动选择最佳数据源）
python3 etf_analyzer.py input/核心ETF十大持仓.csv

# 指定数据源处理
python3 etf_analyzer.py input/核心ETF十大持仓.csv --source 1
```

#### 调试模式
```bash
# 开启调试模式查看详细信息
python3 etf_analyzer.py VOO --debug
```

## 📁 文件说明

### 核心文件
- **`etf_analyzer.py`** - 主程序，功能最全
- **`run.py`** - 用户友好启动器，自动检测依赖
- **`install.py`** - 一键安装脚本
- **`input/`** - 放置要处理的CSV文件
- **`使用说明.md`** - 本说明文件

### 输出文件
- **`us_etf_代码_holdings_时间.txt`** - 单个ETF查询结果
- **`文件名_updated.csv`** - 更新后的CSV文件（包含持仓数据）
- **`文件名_summary_时间.csv`** - CSV汇总文件

## 📋 CSV文件格式要求

CSV文件中需要包含ETF代码列，程序会自动识别：

| 代码 | 名称 | 十大持仓 | 描述 |
|------|------|----------|------|
| VOO  | Vanguard S&P 500 ETF | (自动填充) | 跟踪S&P 500指数 |
| QQQ  | Invesco QQQ Trust | (自动填充) | 跟踪纳斯达克100 |

## 🎯 使用示例

### 示例1：查询单个ETF
```bash
$ python3 etf_analyzer.py VOO

🎯 美股ETF持仓分析工具
==================================================
正在查询美股ETF VOO...

✅ Vanguard S&P 500 Index Fund ETF 前十大持仓:
--------------------------------------------------
 1. NVDA     8.07%
 2. MSFT     7.38%
 3. AAPL     5.77%
 4. AMZN     4.12%
 5. META     3.12%
 6. AVGO     2.57%
 7. GOOGL    2.08%
 8. GOOG     1.68%
 9. BRK-B    1.61%
10. TSLA     1.61%
--------------------------------------------------
✅ 数据已保存到: us_etf_VOO_holdings_20250917_123456.txt
```

### 示例2：查询国际ETF
```bash
$ python3 etf_analyzer.py VEA

✅ Vanguard FTSE Developed Markets Index Fund ETF 前十大持仓:
--------------------------------------------------
 1. SAP.DE   1.15%  # 德国SAP公司
 2. AZN.L    0.86%  # 英国阿斯利康
 3. ROG.SW   0.86%  # 瑞士罗氏
 4. SHEL.L   0.84%  # 英国壳牌
 5. HSBA.L   0.84%  # 英国汇丰
 6. ASML.AS  1.07%  # 荷兰ASML
 7. 005930.KS 0.93% # 韩国三星电子
 8. NESN.SW  0.87%  # 瑞士雀巢
 9. NOVN.SW  0.84%  # 瑞士诺华
10. CBA.AX   0.74%  # 澳洲联邦银行
--------------------------------------------------
```

### 示例3：处理CSV文件
```bash
$ python3 etf_analyzer.py input/核心ETF十大持仓.csv

🎯 美股ETF持仓分析工具
==================================================
正在处理CSV文件: input/核心ETF十大持仓.csv

📋 处理工作表: Sheet1
找到 8 个美股ETF代码
[1/8] 处理 VOO...
  ✅ 成功
[2/8] 处理 SPY...
  ✅ 成功
[3/8] 处理 IVV...
  ✅ 成功
...

🎉 处理完成!
总计: 8 个美股ETF
成功: 8 个
失败: 0 个
成功率: 100.0%

✅ CSV文件已更新: input/核心ETF十大持仓_updated.csv
✅ CSV汇总文件已生成: input/核心ETF十大持仓_summary_20250917_123456.csv
```

## 🌍 支持的ETF类型

### 美股宽基ETF
- **SPY** - SPDR S&P 500 ETF Trust
- **VOO** - Vanguard S&P 500 ETF  
- **IVV** - iShares Core S&P 500 ETF
- **VTI** - Vanguard Total Stock Market ETF
- **QQQ** - Invesco QQQ Trust Series I

### 国际ETF
- **VEA** - Vanguard FTSE Developed Markets ETF
- **IEFA** - iShares Core MSCI EAFE ETF
- **VWO** - Vanguard Emerging Markets ETF

### 行业/主题ETF
- **XLK** - Technology Select Sector SPDR Fund
- **ARKK** - ARK Innovation ETF
- **ICLN** - iShares Global Clean Energy ETF

## 🔧 数据源说明

工具支持3个数据源，会自动选择最佳源：

1. **Yahoo Finance** - 主要数据源，支持全球股票
2. **Vanguard Selenium** - Vanguard官网数据，准确度高
3. **yfinance** - 备用数据源，稳定性好

## ❓ 常见问题

### Q: 为什么有些ETF获取失败？
A: 可能原因：
- 网络连接问题（建议使用代理）
- ETF代码错误（请使用正确的美股代码）
- 数据源暂时无法访问

### Q: 支持代理吗？
A: 是的！工具会自动检测并使用系统代理（如127.0.0.1:8001）

### Q: 国际股票代码是什么意思？
A: 
- `SAP.DE` - 德国法兰克福交易所的SAP公司
- `ASML.AS` - 荷兰阿姆斯特丹交易所的ASML
- `005930.KS` - 韩国交易所的三星电子
- `NESN.SW` - 瑞士交易所的雀巢

### Q: 数据多久更新一次？
A: 数据来源于各大金融网站，通常是实时或日更新

### Q: 可以同时处理多个CSV文件吗？
A: 目前一次只能处理一个CSV文件

## 🔧 环境要求

需要安装以下Python包：
```bash
pip install requests beautifulsoup4 pandas selenium webdriver-manager
```

或者直接运行：
```bash
python3 install.py
```

## 📞 技术支持

如有问题，请检查：
1. 网络连接是否正常（建议使用代理）
2. ETF代码是否正确（美股代码，如VOO、SPY）
3. CSV文件格式是否正确
4. Python版本是否为3.6+

---

**🎉 现在就开始使用吧！只需要一行命令：`python3 etf_analyzer.py`**
