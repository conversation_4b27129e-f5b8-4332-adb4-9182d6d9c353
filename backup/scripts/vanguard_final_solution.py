#!/usr/bin/env python3
"""
Vanguard最终解决方案
基于用户提供的图片，我们知道Vanguard确实有完整的持仓数据
这个脚本将使用最专业的方法来获取这些数据
"""

import requests
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re

class VanguardFinalSolution:
    def __init__(self, proxy=None, debug=False):
        self.proxy = proxy
        self.debug = debug
        self.driver = None
        
    def setup_driver(self):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            # 不使用无头模式，这样可以看到实际的页面加载过程
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            # 如果有代理，添加代理设置
            if self.proxy:
                chrome_options.add_argument(f'--proxy-server={self.proxy}')
            
            # 自动下载ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(60)
            
            if self.debug:
                print("  调试: Chrome WebDriver 已启动（可视模式）")
            
            return True
            
        except Exception as e:
            if self.debug:
                print(f"  调试: WebDriver设置失败: {e}")
            return False
    
    def get_vanguard_holdings_final(self, symbol):
        """最终版本的Vanguard持仓获取"""
        print(f"\n🔍 Vanguard最终解决方案 - {symbol}")
        
        try:
            if not self.driver:
                if not self.setup_driver():
                    return None
            
            # 访问基金主页
            url = f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}"
            print(f"  访问URL: {url}")
            
            self.driver.get(url)
            time.sleep(5)
            
            # 寻找并点击Portfolio composition标签
            try:
                print("  寻找Portfolio composition标签...")
                
                # 尝试多种可能的选择器
                selectors = [
                    "//a[contains(text(), 'Portfolio composition')]",
                    "//button[contains(text(), 'Portfolio composition')]",
                    "//div[contains(text(), 'Portfolio composition')]",
                    "//span[contains(text(), 'Portfolio composition')]",
                    "//a[contains(@href, 'portfolio-composition')]",
                    "//a[contains(text(), 'Holdings')]",
                    "//button[contains(text(), 'Holdings')]"
                ]
                
                portfolio_element = None
                for selector in selectors:
                    try:
                        portfolio_element = WebDriverWait(self.driver, 10).until(
                            EC.element_to_be_clickable((By.XPATH, selector))
                        )
                        print(f"  ✅ 找到Portfolio composition元素: {selector}")
                        break
                    except:
                        continue
                
                if portfolio_element:
                    portfolio_element.click()
                    print("  ✅ 成功点击Portfolio composition")
                    time.sleep(8)  # 等待数据加载
                else:
                    print("  ⚠️ 未找到Portfolio composition标签，尝试直接访问持仓页面")
                    # 直接访问持仓页面
                    holdings_url = f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}/portfolio-composition"
                    self.driver.get(holdings_url)
                    time.sleep(10)
                
            except Exception as e:
                print(f"  ⚠️ 点击Portfolio composition失败: {e}")
                # 尝试直接访问持仓页面
                holdings_url = f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}/portfolio-composition"
                self.driver.get(holdings_url)
                time.sleep(10)
            
            # 等待持仓数据加载
            print("  等待持仓数据加载...")
            time.sleep(15)  # 给足够的时间让JavaScript加载数据
            
            # 尝试滚动页面，触发懒加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(3)
            
            # 获取页面源码
            page_source = self.driver.page_source
            
            # 保存调试文件
            with open(f'vanguard_final_{symbol}.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            print(f"  调试文件已保存: vanguard_final_{symbol}.html")
            
            # 解析持仓数据
            holdings = self.parse_holdings_final(page_source, symbol)
            
            if holdings:
                print(f"  ✅ 成功获取 {len(holdings)} 个持仓")
                return holdings
            else:
                print("  ❌ 未能解析出持仓数据")
                
                # 尝试查找页面中的关键信息
                soup = BeautifulSoup(page_source, 'html.parser')
                page_text = soup.get_text()
                
                # 检查是否包含已知的股票代码
                known_stocks = ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL']
                found_stocks = [stock for stock in known_stocks if stock in page_text]
                
                if found_stocks:
                    print(f"  ℹ️ 页面包含股票代码: {', '.join(found_stocks)}")
                    print("  ℹ️ 数据存在但解析失败，可能需要调整解析逻辑")
                else:
                    print("  ℹ️ 页面不包含预期的股票代码")
                
                return None
            
        except Exception as e:
            print(f"  ❌ 最终解决方案异常: {e}")
            return None
        
        finally:
            if self.driver:
                input("  按Enter键关闭浏览器...")  # 让用户有时间查看页面
                self.driver.quit()
    
    def parse_holdings_final(self, page_source, symbol):
        """最终版本的持仓数据解析"""
        holdings = []
        
        try:
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 方法1: 查找所有表格，详细分析
            tables = soup.find_all('table')
            print(f"  找到 {len(tables)} 个表格")
            
            for i, table in enumerate(tables):
                rows = table.find_all('tr')
                print(f"  表格 {i+1}: {len(rows)} 行")
                
                # 检查表格是否包含持仓数据
                table_text = table.get_text()
                if any(keyword in table_text.lower() for keyword in ['nvda', 'msft', 'aapl', 'holdings', 'ticker']):
                    print(f"  ✅ 表格 {i+1} 可能包含持仓数据")
                    
                    # 详细解析这个表格
                    for j, row in enumerate(rows):
                        cells = row.find_all(['td', 'th'])
                        if len(cells) >= 2:
                            cell_texts = [cell.get_text().strip() for cell in cells]
                            print(f"    行 {j+1}: {cell_texts}")
                            
                            # 查找股票代码模式
                            for k, text in enumerate(cell_texts):
                                if re.match(r'^[A-Z]{2,6}$', text) and text in ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'GOOG', 'AVGO', 'TSLA', 'BRK.B']:
                                    symbol_text = text
                                    name_text = cell_texts[k+1] if k+1 < len(cell_texts) else ""
                                    weight_text = ""

                                    # 查找权重 - 改进的权重查找逻辑
                                    for cell_text in cell_texts:
                                        # 匹配 "7.75 %" 或 "7.75%" 格式
                                        weight_match = re.search(r'(\d+\.\d+)\s*%', cell_text)
                                        if weight_match:
                                            weight_text = weight_match.group(1) + "%"
                                            break

                                    if symbol_text and name_text:
                                        holdings.append({
                                            'rank': len(holdings) + 1,
                                            'symbol': symbol_text,
                                            'name': name_text,
                                            'weight': weight_text or "N/A"
                                        })
                                        print(f"  ✅ 解析到持仓: {symbol_text} - {name_text} ({weight_text})")

                                        if len(holdings) >= 10:
                                            return holdings
            
            # 方法2: 如果表格解析失败，尝试文本解析
            if not holdings:
                print("  表格解析失败，尝试文本解析...")
                page_text = soup.get_text()
                
                # 查找包含百分比的行
                lines = page_text.split('\n')
                for line in lines:
                    if '%' in line and any(stock in line for stock in ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL']):
                        print(f"  找到可能的持仓行: {line.strip()}")
                        
                        # 尝试解析这一行
                        match = re.search(r'([A-Z]{2,6})\s+([^0-9\n]+?)\s+(\d+\.\d+%)', line)
                        if match:
                            symbol_text, name_text, weight_text = match.groups()
                            holdings.append({
                                'rank': len(holdings) + 1,
                                'symbol': symbol_text.strip(),
                                'name': name_text.strip(),
                                'weight': weight_text.strip()
                            })
                            print(f"  ✅ 文本解析到持仓: {symbol_text} - {name_text} ({weight_text})")
                            
                            if len(holdings) >= 10:
                                break
            
        except Exception as e:
            print(f"  解析异常: {e}")
        
        return holdings

def main():
    # 自动检测代理
    proxy = None
    try:
        test_response = requests.get('http://127.0.0.1:8001', timeout=2)
        proxy = 'http://127.0.0.1:8001'
        print(f"🌐 检测到代理: {proxy}")
    except:
        print("🌐 未检测到代理，直接连接")
    
    solver = VanguardFinalSolution(proxy=proxy, debug=True)
    
    # 测试VOO
    holdings = solver.get_vanguard_holdings_final("VOO")
    
    if holdings:
        print(f"\n🎉 成功获取VOO持仓数据!")
        for h in holdings:
            print(f"  {h['rank']}. {h['symbol']} - {h['name']} ({h['weight']})")
    else:
        print(f"\n❌ 未能获取VOO持仓数据")

if __name__ == "__main__":
    main()
