#!/usr/bin/env python3
"""
Selenium测试脚本 - 测试使用Selenium获取Yahoo Finance持仓数据
"""

import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import re

def parse_holdings_from_text(driver):
    """从页面文本中解析持仓数据"""
    try:
        # 获取页面所有文本
        page_text = driver.find_element(By.TAG_NAME, 'body').text
        print("页面文本内容（前2000字符）:")
        print(page_text[:2000])
        print("\n" + "="*50 + "\n")

        # 寻找持仓部分
        lines = page_text.split('\n')
        holdings = []

        # 寻找"Top 10 Holdings"部分
        in_holdings_section = False

        for i, line in enumerate(lines):
            line = line.strip()

            # 检查是否进入持仓部分
            if 'Top 10 Holdings' in line or 'Holdings' in line:
                in_holdings_section = True
                print(f"找到持仓部分标题: {line}")
                continue

            # 如果在持仓部分，寻找股票数据
            if in_holdings_section:
                # 多种模式匹配
                patterns = [
                    r'^([A-Z]{2,5})\s+(.+?)\s+(\d+\.\d+%)\s*$',  # NVDA NVIDIA Corporation 8.07%
                    r'^([A-Z]{2,5})\s*$',  # 只有股票代码的行，下一行可能是公司名
                ]

                for pattern in patterns:
                    match = re.match(pattern, line)
                    if match:
                        if len(match.groups()) == 3:
                            symbol, name, weight = match.groups()
                            # 验证是否是有效的股票代码
                            if symbol in ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'GOOG', 'AVGO', 'TSLA', 'BRK-B']:
                                holdings.append({
                                    'rank': len(holdings) + 1,
                                    'symbol': symbol,
                                    'name': name.strip(),
                                    'weight': weight
                                })
                                print(f"从文本解析: {symbol} - {name.strip()} ({weight})")
                        elif len(match.groups()) == 1:
                            # 只有股票代码，尝试从下一行获取公司名和权重
                            symbol = match.groups()[0]
                            if symbol in ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'GOOG', 'AVGO', 'TSLA', 'BRK-B']:
                                # 查看接下来的几行
                                for j in range(1, 4):
                                    if i + j < len(lines):
                                        next_line = lines[i + j].strip()
                                        # 寻找公司名
                                        if 'Corporation' in next_line or 'Inc.' in next_line or 'Company' in next_line:
                                            name = next_line
                                            # 寻找权重
                                            for k in range(j + 1, j + 3):
                                                if i + k < len(lines):
                                                    weight_line = lines[i + k].strip()
                                                    if re.match(r'\d+\.\d+%', weight_line):
                                                        holdings.append({
                                                            'rank': len(holdings) + 1,
                                                            'symbol': symbol,
                                                            'name': name,
                                                            'weight': weight_line
                                                        })
                                                        print(f"从文本解析(分行): {symbol} - {name} ({weight_line})")
                                                        break
                                            break
                        break

                # 如果已经找到10个持仓，停止
                if len(holdings) >= 10:
                    break

                # 如果遇到其他部分的标题，停止寻找
                if any(keyword in line for keyword in ['Sector Weightings', 'Performance', 'Risk', 'Fund Family']):
                    break

        return holdings if holdings else None

    except Exception as e:
        print(f"文本解析失败: {e}")
        return None

def setup_selenium_driver(proxy=None):
    """设置Selenium Chrome驱动"""
    options = Options()
    options.add_argument('--headless')  # 无头模式
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 设置代理
    if proxy:
        options.add_argument(f'--proxy-server={proxy}')
        print(f"使用代理: {proxy}")
    
    # 自动下载并设置ChromeDriver
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        return driver
    except Exception as e:
        print(f"设置Chrome驱动失败: {e}")
        return None

def get_etf_holdings_selenium(symbol, proxy=None):
    """使用Selenium获取ETF持仓数据"""
    driver = setup_selenium_driver(proxy)
    if not driver:
        return None
    
    try:
        url = f"https://finance.yahoo.com/quote/{symbol}/holdings"
        print(f"访问URL: {url}")
        
        driver.get(url)
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(10)  # 增加等待时间

        # 尝试滚动页面，触发懒加载
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)
        driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)
        
        # 等待持仓表格出现
        try:
            # 尝试多种可能的选择器
            selectors = [
                'table[data-testid="holdings-table"]',
                'table.holdings-table',
                'div[data-testid="holdings"] table',
                'section[data-testid="holdings"] table',
                'div[data-testid="top-holdings"] table',
                'section[data-testid="top-holdings"] table',
                'div.top-holdings table',
                'section.top-holdings table',
            ]
            
            table = None
            all_tables = driver.find_elements(By.TAG_NAME, 'table')
            print(f"页面中总共找到 {len(all_tables)} 个表格")

            # 寻找包含股票代码的表格
            for i, tbl in enumerate(all_tables):
                try:
                    table_text = tbl.text
                    print(f"表格 {i+1} 内容预览: {table_text[:300]}")

                    # 检查是否包含股票代码特征
                    if any(keyword in table_text.upper() for keyword in ['MSFT', 'AAPL', 'NVDA', 'GOOGL', 'AMZN', 'SYMBOL', 'COMPANY', 'ASSETS', '%']):
                        table = tbl
                        print(f"找到疑似持仓表格 {i+1}")
                        break
                except:
                    continue

            # 如果还没找到，尝试寻找包含持仓信息的div或section
            if not table:
                print("在表格中未找到持仓数据，尝试寻找其他元素...")

                # 尝试寻找包含持仓的div
                holding_divs = driver.find_elements(By.XPATH, "//*[contains(text(), 'Top 10 Holdings') or contains(text(), 'Holdings') or contains(text(), 'NVDA') or contains(text(), 'MSFT')]")
                print(f"找到 {len(holding_divs)} 个可能包含持仓信息的元素")

                for i, div in enumerate(holding_divs):
                    try:
                        parent = div.find_element(By.XPATH, "./..")
                        parent_text = parent.text
                        print(f"持仓元素 {i+1} 父级内容: {parent_text[:500]}")

                        # 检查父级元素是否包含完整的持仓列表
                        if 'NVDA' in parent_text and 'MSFT' in parent_text and '%' in parent_text:
                            print(f"找到包含持仓数据的元素 {i+1}")
                            # 尝试在这个元素中找表格
                            tables_in_parent = parent.find_elements(By.TAG_NAME, 'table')
                            if tables_in_parent:
                                table = tables_in_parent[0]
                                print("在持仓元素中找到表格")
                                break
                    except Exception as e:
                        print(f"处理持仓元素 {i+1} 时出错: {e}")
                        continue

            # 如果没找到，尝试原来的选择器方法
            if not table:
                for selector in selectors:
                    try:
                        table = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                        )
                        print(f"找到表格，使用选择器: {selector}")
                        break
                    except:
                        continue
            
            if not table:
                print("未找到持仓表格")
                # 保存页面源码用于调试
                with open(f'selenium_debug_{symbol}.html', 'w', encoding='utf-8') as f:
                    f.write(driver.page_source)
                print(f"页面源码已保存到 selenium_debug_{symbol}.html")

                # 尝试查找页面中的任何文本内容
                page_text = driver.find_element(By.TAG_NAME, 'body').text
                print(f"页面文本内容前500字符: {page_text[:500]}")
                return None
            
            # 解析表格数据
            holdings = []

            if table:
                rows = table.find_elements(By.TAG_NAME, 'tr')
                print(f"找到 {len(rows)} 行数据")

                # 调试：打印表格HTML
                print("表格HTML内容:")
                print(table.get_attribute('outerHTML')[:1000])

                for i, row in enumerate(rows[1:11]):  # 跳过表头，取前10行
                    try:
                        cells = row.find_elements(By.TAG_NAME, 'td')
                        if len(cells) >= 3:
                            symbol_text = cells[0].text.strip()
                            name_text = cells[1].text.strip()
                            weight_text = cells[2].text.strip()

                            if symbol_text and name_text:
                                holdings.append({
                                    'rank': i + 1,
                                    'symbol': symbol_text,
                                    'name': name_text,
                                    'weight': weight_text
                                })
                                print(f"  {i+1}. {symbol_text} - {name_text} ({weight_text})")
                    except Exception as e:
                        print(f"解析第{i+1}行时出错: {e}")
                        continue

            # 如果表格解析失败，尝试其他方法
            if not holdings:
                print("表格解析失败，尝试其他解析方法...")
                holdings = parse_holdings_from_text(driver)

            return holdings if holdings else None
            
        except Exception as e:
            print(f"等待表格超时: {e}")
            # 保存页面源码用于调试
            with open(f'selenium_debug_{symbol}.html', 'w', encoding='utf-8') as f:
                f.write(driver.page_source)
            print(f"页面源码已保存到 selenium_debug_{symbol}.html")
            return None
            
    except Exception as e:
        print(f"Selenium获取数据失败: {e}")
        return None
    finally:
        driver.quit()

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python3 selenium_test.py <ETF代码> [代理地址]")
        print("示例: python3 selenium_test.py SPY")
        print("示例: python3 selenium_test.py SPY http://127.0.0.1:8001")
        return
    
    symbol = sys.argv[1].upper()
    proxy = sys.argv[2] if len(sys.argv) > 2 else None
    
    print(f"🔍 使用Selenium测试获取 {symbol} 的持仓数据")
    print("=" * 50)
    
    holdings = get_etf_holdings_selenium(symbol, proxy)
    
    if holdings:
        print(f"\n✅ 成功获取到 {len(holdings)} 个持仓:")
        print("-" * 80)
        for holding in holdings:
            print(f"{holding['rank']:2d}. {holding['symbol']:<8} {holding['name']:<40} {holding['weight']}")
        print("-" * 80)
    else:
        print("\n❌ 未能获取到持仓数据")

if __name__ == "__main__":
    main()
