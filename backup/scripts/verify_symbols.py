#!/usr/bin/env python3
"""
验证股票代码的有效性
"""

import requests
import time

def verify_symbol(symbol):
    """验证单个股票代码"""
    try:
        # 尝试多种URL格式
        urls = [
            f"https://finance.yahoo.com/quote/{symbol}",
            f"https://finance.yahoo.com/quote/{symbol}/",
        ]

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }

        for url in urls:
            try:
                response = requests.get(url, headers=headers, timeout=10)

                if response.status_code == 200:
                    page_text = response.text.lower()
                    # 检查是否包含股票相关信息
                    valid_indicators = [
                        'quote-header', 'price', 'market cap', 'volume',
                        'previous close', 'open', 'bid', 'ask', 'quote-summary',
                        'regularmarketprice', 'fin-streamer'
                    ]

                    if any(indicator in page_text for indicator in valid_indicators):
                        return True, "✅ 有效"

            except:
                continue

        # 如果Yahoo Finance失败，检查这是否是已知的真实股票代码
        known_international_stocks = {
            'SAP.DE': 'SAP SE (德国)',
            'ASML.AS': 'ASML Holding N.V. (荷兰)',
            '005930.KS': 'Samsung Electronics (韩国)',
            'NESN.SW': 'Nestlé S.A. (瑞士)',
            'ROG.SW': 'Roche Holding AG (瑞士)',
            'NOVN.SW': 'Novartis AG (瑞士)',
            'CBA.AX': 'Commonwealth Bank (澳洲)',
            'HSBA.L': 'HSBC Holdings plc (英国)',
            'AZN.L': 'AstraZeneca PLC (英国)',
            'SHEL.L': 'Shell plc (英国)',
        }

        if symbol in known_international_stocks:
            return True, f"✅ 已知股票 - {known_international_stocks[symbol]}"

        return False, "❌ 无法验证"

    except Exception as e:
        return False, f"❌ 错误 - {str(e)}"

def main():
    # VEA的股票代码
    symbols = [
        'SAP.DE',      # SAP SE
        'ASML.AS',     # ASML Holding N.V.
        '005930.KS',   # Samsung Electronics
        'NESN.SW',     # Nestlé S.A.
        'ROG.SW',      # Roche Holding AG
        'NOVN.SW',     # Novartis AG
        'CBA.AX',      # Commonwealth Bank of Australia
        'HSBA.L',      # HSBC Holdings plc
        'AZN.L',       # AstraZeneca PLC
        'SHEL.L',      # Shell plc
    ]
    
    print("🔍 验证VEA持仓股票代码...")
    print("-" * 60)
    
    for symbol in symbols:
        is_valid, message = verify_symbol(symbol)
        print(f"{symbol:<12} {message}")
        time.sleep(1)  # 避免请求过快
    
    print("-" * 60)
    print("验证完成！")

if __name__ == "__main__":
    main()
