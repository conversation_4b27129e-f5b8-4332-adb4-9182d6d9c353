#!/usr/bin/env python3
"""
多数据源ETF持仓获取测试脚本
专门测试各个数据源的可用性
"""

import requests
import time
from bs4 import BeautifulSoup
import re
import json

class MultiSourceETFTester:
    def __init__(self, proxy=None):
        self.proxy = proxy
        self.session = requests.Session()
        if proxy:
            self.session.proxies = {'http': proxy, 'https': proxy}
        
        # 设置通用请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
    
    def test_yahoo_finance(self, symbol):
        """测试Yahoo Finance"""
        print(f"\n🔍 测试Yahoo Finance - {symbol}")
        try:
            url = f"https://finance.yahoo.com/quote/{symbol}/holdings"
            response = self.session.get(url, timeout=15)
            print(f"  状态码: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text()
                
                # 查找Top Holdings
                if "Top 10 Holdings" in page_text or "Top Holdings" in page_text:
                    print("  ✅ 找到持仓数据区域")
                    
                    # 提取持仓数据
                    pattern = r'([A-Z]{2,6})\s+([^0-9\n]+?)\s+(\d+\.\d+%)'
                    matches = re.findall(pattern, page_text)
                    
                    holdings = []
                    known_stocks = ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'TSLA', 'ROKU', 'COIN']
                    
                    for match in matches:
                        symbol_part, name_part, weight_part = match
                        if symbol_part in known_stocks and len(holdings) < 5:
                            holdings.append(f"{symbol_part} - {name_part.strip()} ({weight_part})")
                    
                    if holdings:
                        print(f"  ✅ 成功解析 {len(holdings)} 个持仓:")
                        for h in holdings:
                            print(f"    {h}")
                        return True
                    else:
                        print("  ❌ 未能解析出持仓数据")
                else:
                    print("  ❌ 未找到持仓数据区域")
            else:
                print(f"  ❌ 请求失败，状态码: {response.status_code}")
        
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        return False
    
    def test_morningstar(self, symbol):
        """测试Morningstar"""
        print(f"\n🔍 测试Morningstar - {symbol}")
        try:
            urls = [
                f"https://www.morningstar.com/etfs/{symbol.lower()}",
                f"https://www.morningstar.com/etfs/xnas/{symbol.lower()}",
                f"https://www.morningstar.com/funds/{symbol.lower()}"
            ]
            
            for url in urls:
                try:
                    print(f"  尝试URL: {url}")
                    response = self.session.get(url, timeout=15)
                    print(f"  状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        page_text = soup.get_text()
                        
                        # 查找持仓相关内容
                        if any(keyword in page_text.lower() for keyword in ['holdings', 'portfolio', 'top 10']):
                            print("  ✅ 找到持仓相关内容")
                            
                            # 查找表格
                            tables = soup.find_all('table')
                            print(f"  找到 {len(tables)} 个表格")
                            
                            # 查找包含股票代码的内容
                            known_stocks = ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'TSLA']
                            found_stocks = []
                            for stock in known_stocks:
                                if stock in page_text:
                                    found_stocks.append(stock)
                            
                            if found_stocks:
                                print(f"  ✅ 找到股票代码: {', '.join(found_stocks[:5])}")
                                return True
                            else:
                                print("  ❌ 未找到已知股票代码")
                        else:
                            print("  ❌ 未找到持仓相关内容")
                    
                except Exception as e:
                    print(f"  URL异常: {e}")
                    continue
        
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        return False
    
    def test_etf_com(self, symbol):
        """测试ETF.com"""
        print(f"\n🔍 测试ETF.com - {symbol}")
        try:
            urls = [
                f"https://www.etf.com/{symbol.upper()}",
                f"https://www.etf.com/funds/{symbol.upper()}",
                f"https://etfdb.com/etf/{symbol.upper()}/"
            ]
            
            for url in urls:
                try:
                    print(f"  尝试URL: {url}")
                    response = self.session.get(url, timeout=15)
                    print(f"  状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        page_text = soup.get_text()
                        
                        # 查找持仓相关内容
                        if any(keyword in page_text.lower() for keyword in ['holdings', 'top holdings', 'portfolio']):
                            print("  ✅ 找到持仓相关内容")
                            
                            # 查找已知股票
                            known_stocks = ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'TSLA']
                            found_stocks = []
                            for stock in known_stocks:
                                if stock in page_text:
                                    found_stocks.append(stock)
                            
                            if found_stocks:
                                print(f"  ✅ 找到股票代码: {', '.join(found_stocks[:5])}")
                                return True
                            else:
                                print("  ❌ 未找到已知股票代码")
                        else:
                            print("  ❌ 未找到持仓相关内容")
                    elif response.status_code == 403:
                        print("  ❌ 403 Forbidden - 被反爬虫阻止")
                    
                except Exception as e:
                    print(f"  URL异常: {e}")
                    continue
        
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        return False
    
    def test_vanguard_api(self, symbol):
        """测试Vanguard API"""
        print(f"\n🔍 测试Vanguard API - {symbol}")
        try:
            # 只测试Vanguard的ETF
            if not symbol.upper().startswith('V'):
                print("  ⏭️ 跳过非Vanguard ETF")
                return False
            
            api_urls = [
                f"https://api.vanguard.com/rs/gre/gra/1.7.0/datasets/{symbol.upper()}/portfolio-holdings",
                f"https://investor.vanguard.com/rs/gre/gra/1.7.0/datasets/{symbol.upper()}/portfolio-holdings",
                f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}/portfolio-holdings/csv"
            ]
            
            for url in api_urls:
                try:
                    print(f"  尝试API: {url}")
                    response = self.session.get(url, timeout=15)
                    print(f"  状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        # 尝试解析JSON
                        try:
                            data = response.json()
                            print(f"  ✅ 成功获取JSON数据，键: {list(data.keys())}")
                            return True
                        except:
                            # 尝试解析CSV
                            if 'symbol' in response.text.lower() or 'ticker' in response.text.lower():
                                print("  ✅ 成功获取CSV数据")
                                return True
                            else:
                                print("  ❌ 数据格式不明")
                    
                except Exception as e:
                    print(f"  API异常: {e}")
                    continue
        
        except Exception as e:
            print(f"  ❌ 异常: {e}")
        
        return False

def main():
    # 自动检测代理
    proxy = None
    try:
        test_response = requests.get('http://127.0.0.1:8001', timeout=2)
        proxy = 'http://127.0.0.1:8001'
        print(f"🌐 检测到代理: {proxy}")
    except:
        print("🌐 未检测到代理，直接连接")
    
    tester = MultiSourceETFTester(proxy=proxy)
    
    # 测试多个ETF
    test_symbols = ['VOO', 'SPY', 'QQQ', 'ARKK', 'VTI']
    
    results = {}
    
    for symbol in test_symbols:
        print(f"\n{'='*60}")
        print(f"测试ETF: {symbol}")
        print('='*60)
        
        results[symbol] = {
            'yahoo_finance': tester.test_yahoo_finance(symbol),
            'morningstar': tester.test_morningstar(symbol),
            'etf_com': tester.test_etf_com(symbol),
            'vanguard_api': tester.test_vanguard_api(symbol)
        }
        
        time.sleep(2)  # 避免请求过快
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print('='*60)
    
    for symbol, result in results.items():
        print(f"\n{symbol}:")
        for source, success in result.items():
            status = "✅" if success else "❌"
            print(f"  {source}: {status}")
    
    # 统计成功率
    print(f"\n{'='*60}")
    print("数据源成功率统计")
    print('='*60)
    
    sources = ['yahoo_finance', 'morningstar', 'etf_com', 'vanguard_api']
    for source in sources:
        success_count = sum(1 for result in results.values() if result[source])
        total_count = len(results)
        success_rate = (success_count / total_count) * 100
        print(f"{source}: {success_count}/{total_count} ({success_rate:.1f}%)")

if __name__ == "__main__":
    main()
