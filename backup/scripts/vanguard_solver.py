#!/usr/bin/env python3
"""
Vanguard数据获取解决方案
专门解决Vanguard网站的持仓数据获取问题
"""

import requests
import json
import re
from bs4 import BeautifulSoup
import time

class VanguardSolver:
    def __init__(self, proxy=None, debug=False):
        self.proxy = proxy
        self.debug = debug
        self.session = requests.Session()
        
        if proxy:
            self.session.proxies = {'http': proxy, 'https': proxy}
        
        # 设置真实的浏览器请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
    
    def get_vanguard_holdings(self, symbol):
        """获取Vanguard ETF持仓数据"""
        print(f"\n🔍 Vanguard解决方案测试 - {symbol}")
        
        try:
            # 第一步：获取基金页面和基础信息
            fund_info = self.get_fund_info(symbol)
            if not fund_info:
                print("  ❌ 无法获取基金基础信息")
                return None
            
            fund_id = fund_info.get('fundId')
            print(f"  ✅ 获取到基金ID: {fund_id}")
            
            # 第二步：尝试多种方法获取持仓数据
            holdings = None
            
            # 方法1: 尝试持仓API
            holdings = self.get_holdings_from_api(fund_id, symbol)
            if holdings:
                print(f"  ✅ 通过API获取到 {len(holdings)} 个持仓")
                return holdings
            
            # 方法2: 尝试CSV下载
            holdings = self.get_holdings_from_csv(symbol)
            if holdings:
                print(f"  ✅ 通过CSV获取到 {len(holdings)} 个持仓")
                return holdings
            
            # 方法3: 尝试持仓页面
            holdings = self.get_holdings_from_page(symbol)
            if holdings:
                print(f"  ✅ 通过页面解析获取到 {len(holdings)} 个持仓")
                return holdings
            
            print("  ❌ 所有方法都无法获取持仓数据")
            return None
            
        except Exception as e:
            print(f"  ❌ Vanguard解决方案异常: {e}")
            return None
    
    def get_fund_info(self, symbol):
        """从Vanguard页面获取基金基础信息"""
        try:
            url = f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}"
            response = self.session.get(url, timeout=20)
            
            if response.status_code == 200:
                # 查找JSON数据
                json_match = re.search(r'<script id="fundProfileData" type="application/json">(.*?)</script>', response.text, re.DOTALL)
                if json_match:
                    try:
                        fund_data = json.loads(json_match.group(1))
                        fund_profile = fund_data.get('fundProfile', {})
                        
                        if self.debug:
                            print(f"  调试: 基金信息 - {fund_profile.get('longName', 'Unknown')}")
                        
                        return fund_profile
                    except json.JSONDecodeError as e:
                        if self.debug:
                            print(f"  调试: JSON解析失败: {e}")
            
            return None
            
        except Exception as e:
            if self.debug:
                print(f"  调试: 获取基金信息失败: {e}")
            return None
    
    def get_holdings_from_api(self, fund_id, symbol):
        """尝试从API获取持仓数据"""
        try:
            # 尝试多个可能的API端点
            api_urls = [
                f"https://investor.vanguard.com/rs/gre/gra/1.7.0/datasets/{fund_id}/portfolio-holdings",
                f"https://investor.vanguard.com/rs/gre/gra/1.8.0/datasets/{fund_id}/portfolio-holdings",
                f"https://investor.vanguard.com/api/v1/fund/{fund_id}/holdings",
                f"https://investor.vanguard.com/api/fund/{symbol.upper()}/holdings",
                f"https://investor.vanguard.com/rs/gre/gra/1.7.0/datasets/{symbol.upper()}/portfolio-holdings"
            ]
            
            for api_url in api_urls:
                try:
                    if self.debug:
                        print(f"  调试: 尝试API - {api_url}")
                    
                    response = self.session.get(api_url, timeout=15)
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            holdings = self.parse_api_holdings(data)
                            if holdings:
                                return holdings
                        except:
                            # 可能是CSV格式
                            holdings = self.parse_csv_content(response.text)
                            if holdings:
                                return holdings
                    
                except Exception as e:
                    if self.debug:
                        print(f"  调试: API请求失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            if self.debug:
                print(f"  调试: API方法失败: {e}")
            return None
    
    def get_holdings_from_csv(self, symbol):
        """尝试下载CSV文件"""
        try:
            csv_urls = [
                f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}/portfolio-holdings/csv",
                f"https://investor.vanguard.com/rs/gre/gra/1.3.0/documents/{symbol.upper()}_holdings.csv",
                f"https://investor.vanguard.com/etf/profile/{symbol.upper()}/portfolio-holdings/csv"
            ]
            
            for csv_url in csv_urls:
                try:
                    if self.debug:
                        print(f"  调试: 尝试CSV - {csv_url}")
                    
                    response = self.session.get(csv_url, timeout=15)
                    if response.status_code == 200:
                        holdings = self.parse_csv_content(response.text)
                        if holdings:
                            return holdings
                    
                except Exception as e:
                    if self.debug:
                        print(f"  调试: CSV请求失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            if self.debug:
                print(f"  调试: CSV方法失败: {e}")
            return None
    
    def get_holdings_from_page(self, symbol):
        """从持仓页面解析数据"""
        try:
            page_urls = [
                f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}/portfolio-composition",
                f"https://investor.vanguard.com/etf/profile/{symbol.upper()}/portfolio-composition"
            ]
            
            for page_url in page_urls:
                try:
                    if self.debug:
                        print(f"  调试: 尝试页面 - {page_url}")
                    
                    response = self.session.get(page_url, timeout=20)
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')
                        
                        if self.debug:
                            with open(f'debug_vanguard_holdings_{symbol}.html', 'w', encoding='utf-8') as f:
                                f.write(response.text)
                            print(f"  调试: 持仓页面已保存")
                        
                        holdings = self.parse_page_holdings(soup)
                        if holdings:
                            return holdings
                    
                except Exception as e:
                    if self.debug:
                        print(f"  调试: 页面请求失败: {e}")
                    continue
            
            return None
            
        except Exception as e:
            if self.debug:
                print(f"  调试: 页面方法失败: {e}")
            return None
    
    def parse_api_holdings(self, data):
        """解析API返回的持仓数据"""
        holdings = []
        try:
            # 尝试多种可能的数据结构
            possible_paths = [
                ['fund', 'portfolio', 'holdings'],
                ['portfolio', 'holdings'],
                ['holdings'],
                ['data', 'holdings'],
                ['fundProfile', 'holdings'],
                ['portfolioComposition', 'holdings']
            ]
            
            holdings_data = None
            for path in possible_paths:
                temp_data = data
                try:
                    for key in path:
                        temp_data = temp_data[key]
                    holdings_data = temp_data
                    break
                except (KeyError, TypeError):
                    continue
            
            if holdings_data and isinstance(holdings_data, list):
                for i, holding in enumerate(holdings_data[:10]):
                    if isinstance(holding, dict):
                        symbol = holding.get('ticker', holding.get('symbol', holding.get('code', '')))
                        name = holding.get('name', holding.get('description', holding.get('companyName', '')))
                        weight = holding.get('weight', holding.get('percentage', holding.get('allocation', 0)))
                        
                        if symbol and name:
                            holdings.append({
                                'rank': i + 1,
                                'symbol': str(symbol).strip(),
                                'name': str(name).strip(),
                                'weight': f"{float(weight):.2f}%" if isinstance(weight, (int, float)) else str(weight)
                            })
            
        except Exception as e:
            if self.debug:
                print(f"  调试: API数据解析失败: {e}")
        
        return holdings if len(holdings) > 0 else None
    
    def parse_csv_content(self, csv_text):
        """解析CSV内容"""
        holdings = []
        try:
            lines = csv_text.strip().split('\n')
            if len(lines) < 2:
                return None
            
            # 查找表头
            header_line = None
            for i, line in enumerate(lines):
                if any(keyword in line.lower() for keyword in ['ticker', 'symbol', 'holding', 'name']):
                    header_line = i
                    break
            
            if header_line is None:
                return None
            
            headers = [h.strip().strip('"') for h in lines[header_line].split(',')]
            
            # 查找列索引
            symbol_idx = name_idx = weight_idx = -1
            for i, header in enumerate(headers):
                header_lower = header.lower()
                if 'ticker' in header_lower or 'symbol' in header_lower:
                    symbol_idx = i
                elif 'name' in header_lower or 'holding' in header_lower:
                    name_idx = i
                elif 'weight' in header_lower or '%' in header_lower or 'allocation' in header_lower:
                    weight_idx = i
            
            if symbol_idx == -1 or name_idx == -1:
                return None
            
            # 解析数据行
            for line in lines[header_line + 1:header_line + 11]:  # 取前10行
                try:
                    cells = [c.strip().strip('"') for c in line.split(',')]
                    if len(cells) > max(symbol_idx, name_idx):
                        symbol = cells[symbol_idx]
                        name = cells[name_idx]
                        weight = cells[weight_idx] if weight_idx != -1 and weight_idx < len(cells) else ""
                        
                        if symbol and name:
                            holdings.append({
                                'rank': len(holdings) + 1,
                                'symbol': symbol,
                                'name': name,
                                'weight': weight
                            })
                except:
                    continue
            
        except Exception as e:
            if self.debug:
                print(f"  调试: CSV解析失败: {e}")
        
        return holdings if len(holdings) > 0 else None
    
    def parse_page_holdings(self, soup):
        """解析页面持仓数据"""
        holdings = []
        try:
            # 查找表格
            tables = soup.find_all('table')
            for table in tables:
                rows = table.find_all('tr')
                for row in rows[1:11]:  # 跳过表头，取前10行
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 3:
                        symbol = cells[0].get_text().strip()
                        name = cells[1].get_text().strip()
                        weight = cells[2].get_text().strip()
                        
                        if symbol and name and re.match(r'^[A-Z]{2,6}$', symbol):
                            holdings.append({
                                'rank': len(holdings) + 1,
                                'symbol': symbol,
                                'name': name,
                                'weight': weight
                            })
                
                if holdings:
                    break
            
        except Exception as e:
            if self.debug:
                print(f"  调试: 页面解析失败: {e}")
        
        return holdings if len(holdings) > 0 else None

def main():
    # 自动检测代理
    proxy = None
    try:
        test_response = requests.get('http://127.0.0.1:8001', timeout=2)
        proxy = 'http://127.0.0.1:8001'
        print(f"🌐 检测到代理: {proxy}")
    except:
        print("🌐 未检测到代理，直接连接")
    
    solver = VanguardSolver(proxy=proxy, debug=True)
    
    # 测试多个Vanguard ETF
    test_symbols = ['VOO', 'VTI', 'VEA', 'VWO', 'VGT']
    
    for symbol in test_symbols:
        holdings = solver.get_vanguard_holdings(symbol)
        if holdings:
            print(f"\n✅ {symbol} Vanguard持仓数据获取成功!")
            for h in holdings:
                print(f"  {h['rank']}. {h['symbol']} - {h['name']} ({h['weight']})")
        else:
            print(f"\n❌ {symbol} Vanguard持仓数据获取失败")
        
        time.sleep(2)  # 避免请求过快

if __name__ == "__main__":
    main()
