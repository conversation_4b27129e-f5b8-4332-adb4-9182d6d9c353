#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建美股ETF示例Excel文件
"""

import pandas as pd
import os

def create_sample_excel():
    """创建示例Excel文件"""
    
    # 大盘指数ETF
    large_cap_etfs = [
        {'ETF代码': 'SPY', 'ETF名称': 'SPDR S&P 500 ETF Trust', '描述': '跟踪标普500指数，美国最大的ETF之一'},
        {'ETF代码': 'VOO', 'ETF名称': 'Vanguard S&P 500 ETF', '描述': '低费率的标普500 ETF'},
        {'ETF代码': 'IVV', 'ETF名称': 'iShares Core S&P 500 ETF', '描述': 'iShares的标普500 ETF'},
        {'ETF代码': 'VTI', 'ETF名称': 'Vanguard Total Stock Market ETF', '描述': '跟踪整个美股市场'},
        {'ETF代码': 'ITOT', 'ETF名称': 'iShares Core S&P Total US Stock Market ETF', '描述': '全美股市场ETF'},
        {'ETF代码': 'SPTM', 'ETF名称': 'SPDR Portfolio S&P 1500 Composite Stock Market ETF', '描述': '标普1500指数ETF'},
    ]
    
    # 科技股ETF
    tech_etfs = [
        {'ETF代码': 'QQQ', 'ETF名称': 'Invesco QQQ Trust', '描述': '跟踪纳斯达克100指数'},
        {'ETF代码': 'XLK', 'ETF名称': 'Technology Select Sector SPDR Fund', '描述': '科技行业精选ETF'},
        {'ETF代码': 'VGT', 'ETF名称': 'Vanguard Information Technology ETF', '描述': 'Vanguard科技ETF'},
        {'ETF代码': 'FTEC', 'ETF名称': 'Fidelity MSCI Information Technology Index ETF', '描述': 'Fidelity科技ETF'},
        {'ETF代码': 'IYW', 'ETF名称': 'iShares U.S. Technology ETF', '描述': 'iShares美国科技ETF'},
    ]
    
    # 行业ETF
    sector_etfs = [
        {'ETF代码': 'XLF', 'ETF名称': 'Financial Select Sector SPDR Fund', '描述': '金融行业ETF'},
        {'ETF代码': 'XLE', 'ETF名称': 'Energy Select Sector SPDR Fund', '描述': '能源行业ETF'},
        {'ETF代码': 'XLV', 'ETF名称': 'Health Care Select Sector SPDR Fund', '描述': '医疗保健行业ETF'},
        {'ETF代码': 'XLI', 'ETF名称': 'Industrial Select Sector SPDR Fund', '描述': '工业行业ETF'},
        {'ETF代码': 'XLP', 'ETF名称': 'Consumer Staples Select Sector SPDR Fund', '描述': '消费必需品ETF'},
        {'ETF代码': 'XLY', 'ETF名称': 'Consumer Discretionary Select Sector SPDR Fund', '描述': '消费可选ETF'},
    ]
    
    # 国际ETF
    international_etfs = [
        {'ETF代码': 'EFA', 'ETF名称': 'iShares MSCI EAFE ETF', '描述': '发达市场国际ETF'},
        {'ETF代码': 'VWO', 'ETF名称': 'Vanguard Emerging Markets Stock Index Fund', '描述': '新兴市场ETF'},
        {'ETF代码': 'VEA', 'ETF名称': 'Vanguard FTSE Developed Markets ETF', '描述': '发达市场ETF'},
        {'ETF代码': 'IEFA', 'ETF名称': 'iShares Core MSCI EAFE IMI Index ETF', '描述': '核心发达市场ETF'},
        {'ETF代码': 'VGK', 'ETF名称': 'Vanguard FTSE Europe ETF', '描述': '欧洲市场ETF'},
    ]
    
    # 创建Excel文件
    excel_file = 'input/美股ETF十大持仓.xlsx'
    
    # 确保input目录存在
    os.makedirs('input', exist_ok=True)
    
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        pd.DataFrame(large_cap_etfs).to_excel(writer, sheet_name='大盘指数ETF', index=False)
        pd.DataFrame(tech_etfs).to_excel(writer, sheet_name='科技股ETF', index=False)
        pd.DataFrame(sector_etfs).to_excel(writer, sheet_name='行业ETF', index=False)
        pd.DataFrame(international_etfs).to_excel(writer, sheet_name='国际ETF', index=False)
    
    print(f"✅ 示例Excel文件已创建: {excel_file}")
    print("📋 包含以下工作表:")
    print("   - 大盘指数ETF (6个ETF)")
    print("   - 科技股ETF (5个ETF)")
    print("   - 行业ETF (6个ETF)")
    print("   - 国际ETF (5个ETF)")
    print(f"📊 总计: {len(large_cap_etfs) + len(tech_etfs) + len(sector_etfs) + len(international_etfs)} 个美股ETF")

if __name__ == "__main__":
    create_sample_excel()
