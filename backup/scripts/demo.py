#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美股ETF持仓分析工具 - 演示脚本
"""

import subprocess
import sys
import os

def run_demo():
    """运行演示"""
    print("🎯 美股ETF持仓分析工具 - 功能演示")
    print("=" * 60)
    
    print("\n📋 1. 查询单个美股ETF - SPY")
    print("-" * 40)
    result = subprocess.run([sys.executable, 'us_etf_analyzer.py', 'SPY'], 
                          capture_output=True, text=True)
    print(result.stdout)
    
    print("\n📋 2. 查询单个美股ETF - QQQ")
    print("-" * 40)
    result = subprocess.run([sys.executable, 'us_etf_analyzer.py', 'QQQ'], 
                          capture_output=True, text=True)
    print(result.stdout)
    
    print("\n📋 3. 查询单个美股ETF - VTI")
    print("-" * 40)
    result = subprocess.run([sys.executable, 'us_etf_analyzer.py', 'VTI'], 
                          capture_output=True, text=True)
    print(result.stdout)
    
    print("\n📋 4. 批量处理Excel文件")
    print("-" * 40)
    if os.path.exists('input/美股ETF十大持仓.xlsx'):
        print("处理Excel文件: input/美股ETF十大持仓.xlsx")
        result = subprocess.run([sys.executable, 'us_etf_analyzer.py', 
                               'input/美股ETF十大持仓.xlsx'], 
                              capture_output=True, text=True)
        # 只显示关键信息
        lines = result.stdout.split('\n')
        for line in lines:
            if any(keyword in line for keyword in ['处理完成', '总计', '成功', '失败', '成功率', 'Excel文件已更新', 'CSV汇总文件已生成']):
                print(line)
    else:
        print("❌ 示例Excel文件不存在")
    
    print("\n📋 5. 查看生成的文件")
    print("-" * 40)
    
    # 查看文本文件
    txt_files = [f for f in os.listdir('.') if f.startswith('us_etf_') and f.endswith('.txt')]
    if txt_files:
        print(f"✅ 生成的文本文件: {len(txt_files)} 个")
        for f in txt_files[:3]:  # 只显示前3个
            print(f"   - {f}")
    
    # 查看Excel和CSV文件
    if os.path.exists('input'):
        excel_files = [f for f in os.listdir('input') if f.endswith('_updated.xlsx')]
        csv_files = [f for f in os.listdir('input') if f.endswith('_summary_*.csv')]
        
        if excel_files:
            print(f"✅ 更新的Excel文件: {len(excel_files)} 个")
            for f in excel_files:
                print(f"   - input/{f}")
        
        if csv_files:
            print(f"✅ CSV汇总文件: {len(csv_files)} 个")
            for f in csv_files:
                print(f"   - input/{f}")
    
    print("\n🎉 演示完成！")
    print("\n📖 使用说明:")
    print("1. 单个查询: python3 us_etf_analyzer.py <ETF代码>")
    print("2. 批量处理: python3 us_etf_analyzer.py <Excel文件路径>")
    print("3. 交互模式: python3 us_etf_analyzer.py")
    print("\n🌟 支持的热门美股ETF:")
    print("   SPY, QQQ, VTI, VOO, IVV, XLK, XLF, XLE, EFA, VWO 等")

if __name__ == "__main__":
    run_demo()
