# 📊 美股ETF持仓分析工具

一键获取美股ETF前十大持仓数据，支持单个查询和批量Excel处理。

## 🚀 快速开始

### 1. 一键安装
```bash
python3 install.py
```

### 2. 开始使用
```bash
# 交互式使用（推荐新手）
python3 us_etf_analyzer.py

# 查询单个美股ETF
python3 us_etf_analyzer.py SPY

# 处理Excel文件
python3 us_etf_analyzer.py input/美股ETF列表.xlsx
```

## ✨ 主要功能

- 🔍 **单个美股ETF查询** - 输入代码即可获取持仓
- 📊 **批量Excel处理** - 一次处理多个美股ETF
- 🌍 **多数据源支持** - Yahoo Finance、Morningstar等
- 📁 **多工作表支持** - 处理复杂Excel文件
- 💾 **多格式输出** - Excel、CSV格式

## 📁 文件结构

```
U_ETF/
├── us_etf_analyzer.py # 主程序
├── install.py         # 一键安装脚本
├── README.md          # 本文件
├── 使用说明.md        # 详细使用说明
├── input/             # 输入文件夹
└── output/            # 输出文件夹
```

## 🎯 使用示例

### 查询SPDR S&P 500 ETF
```bash
$ python3 us_etf_analyzer.py SPY

✅ SPDR S&P 500 ETF Trust 前十大持仓:
 1. MSFT     Microsoft Corporation           7.12%
 2. AAPL     Apple Inc.                      6.89%
 3. NVDA     NVIDIA Corporation              6.45%
 4. AMZN     Amazon.com Inc.                 3.21%
 5. GOOGL    Alphabet Inc. Class A           2.98%
 ...
```

### 批量处理Excel文件
```bash
$ python3 us_etf_analyzer.py input/美股ETF列表.xlsx

🎉 处理完成!
总计: 25 个美股ETF
成功: 22 个 (88.0%)
✅ 结果已保存到更新的Excel文件
```

## 🌍 支持的美股ETF类型

- 🇺🇸 **大盘指数ETF** - SPY、VOO、IVV等
- 📈 **科技股ETF** - QQQ、XLK、VGT等
- 🏭 **行业ETF** - XLF、XLE、XLV等
- 🌐 **国际ETF** - VEA、VWO、EFA等
- 💎 **价值/成长ETF** - VTV、VUG、IWF等

## 📋 环境要求

- Python 3.6+
- 网络连接

依赖包会自动安装：
- requests
- beautifulsoup4
- pandas
- openpyxl

## 🌟 热门美股ETF代码

| 代码 | 名称 | 类型 |
|------|------|------|
| SPY | SPDR S&P 500 ETF | 大盘指数 |
| QQQ | Invesco QQQ Trust | 科技股 |
| VTI | Vanguard Total Stock Market ETF | 全市场 |
| IWM | iShares Russell 2000 ETF | 小盘股 |
| EFA | iShares MSCI EAFE ETF | 国际发达市场 |
| VWO | Vanguard Emerging Markets ETF | 新兴市场 |
| XLF | Financial Select Sector SPDR Fund | 金融行业 |
| XLE | Energy Select Sector SPDR Fund | 能源行业 |

## 📖 详细说明

查看 `使用说明.md` 获取完整的使用指南和示例。

---

**🎉 立即开始：`python3 install.py` 然后 `python3 us_etf_analyzer.py`**
