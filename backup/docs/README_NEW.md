# 🎯 美股ETF持仓分析工具

一个功能强大、用户友好的美股ETF前十大持仓分析工具，支持多种数据源，适合投资者、分析师和金融从业者使用。

## ✨ 功能特点

- 🎯 **精准数据获取** - 获取美股ETF前十大持仓的详细信息
- 🌐 **多数据源支持** - 11个数据源确保数据获取成功率
- 📊 **批量处理** - 支持Excel文件批量处理多个ETF
- 🔄 **智能代理** - 自动检测和配置网络代理
- 📋 **详细展示** - 清晰的持仓信息展示和保存
- 🚀 **简单易用** - 提供图形化启动器，小白也能轻松使用

## 🚀 快速开始

### 方法一：使用简化启动器（推荐小白用户）

```bash
python3 run.py
```

启动后会看到友好的菜单界面，按提示操作即可。

### 方法二：命令行使用（高级用户）

```bash
# 查询单个ETF
python3 us_etf_analyzer.py SPY

# 指定数据源
python3 us_etf_analyzer.py SPY --source 2

# 处理Excel文件
python3 us_etf_analyzer.py input/美股ETF十大持仓.xlsx --source 3
```

## 📦 安装说明

### 自动安装（推荐）
运行 `python3 run.py`，程序会自动检测并安装缺失的依赖。

### 手动安装
```bash
pip install -r requirements.txt
```

### 依赖包列表
- requests - HTTP请求
- beautifulsoup4 - HTML解析
- pandas - 数据处理
- openpyxl - Excel文件处理
- yfinance - Yahoo Finance数据
- selenium - 浏览器自动化
- lxml - XML/HTML解析

## 📊 数据源详解

| 编号 | 数据源 | 特点 | 推荐度 |
|------|--------|------|--------|
| 1 | yfinance库 | 快速，基础验证 | ⭐⭐ |
| 2 | Vanguard Selenium | 官方数据，最准确，较慢 | ⭐⭐⭐⭐⭐ |
| 3 | Yahoo Finance | 稳定可靠，速度快 | ⭐⭐⭐⭐⭐ |
| 4 | Vanguard CSV | 官方CSV数据 | ⭐⭐⭐ |
| 5 | Vanguard官网 | 官方网站 | ⭐⭐⭐ |
| 6 | ETF.com | 第三方数据 | ⭐⭐ |
| 7 | Morningstar | 专业数据 | ⭐⭐⭐ |
| 8-11 | 其他数据源 | 备用选择 | ⭐⭐ |
| 0 | 自动模式 | 智能选择最佳数据源 | ⭐⭐⭐⭐ |

**💡 使用建议：**
- **日常查询**：使用数据源3 (Yahoo Finance) - 快速稳定
- **重要分析**：使用数据源2 (Vanguard Selenium) - 最准确
- **批量处理**：使用自动模式 - 智能容错

## 📋 使用示例

### 1. 查询单个ETF
```bash
# 查询SPY的持仓
python3 us_etf_analyzer.py SPY --source 3

# 输出示例：
✅ SPDR S&P 500 ETF 前十大持仓:
--------------------------------------------------------------------------------
 1. NVDA     NVIDIA Corporation                  7.74%
 2. MSFT     Microsoft Corporation               6.87%
 3. AAPL     Apple Inc.                          6.32%
 4. AMZN     Amazon.com, Inc.                    3.94%
 5. META     Meta Platforms, Inc.                2.92%
 ...
```

### 2. 批量处理Excel文件
```bash
python3 us_etf_analyzer.py input/美股ETF十大持仓.xlsx --source 2 --debug
```

### 3. 交互模式
```bash
python3 us_etf_analyzer.py
# 然后按提示选择操作
```

## 📁 文件结构

```
U_ETF/
├── run.py                    # 简化启动器（推荐小白使用）
├── us_etf_analyzer.py        # 主程序
├── requirements.txt          # 依赖列表
├── README.md                # 说明文档
├── input/                   # 输入文件夹
│   └── 美股ETF十大持仓.xlsx    # 示例Excel文件
└── output/                  # 输出文件夹（自动创建）
    └── us_etf_*.txt         # 结果文件
```

## 📊 Excel文件格式

Excel文件支持多个工作表，每个工作表应包含ETF代码。参考示例：

| ETF代码 | 名称 | 备注 |
|---------|------|------|
| SPY | SPDR S&P 500 ETF | 标普500 |
| QQQ | Invesco QQQ Trust | 纳斯达克100 |
| VTI | Vanguard Total Stock Market ETF | 全市场 |

## 🔧 高级功能

### 命令行参数
```bash
python3 us_etf_analyzer.py [ETF代码/Excel文件] [选项]

选项：
  --source N     指定数据源 (1-11, 0为自动)
  --debug        开启调试模式
  --help         显示帮助信息
```

### 代理设置
程序会自动检测以下代理：
- http://127.0.0.1:8001
- http://127.0.0.1:7890
- 系统环境变量中的代理设置

## 🐛 常见问题

### Q: 获取数据失败怎么办？
A: 尝试以下解决方案：
1. 切换到推荐的数据源（2或3）
2. 检查网络连接
3. 开启调试模式查看详细错误信息
4. 使用自动模式让程序智能选择数据源

### Q: 需要代理才能访问吗？
A: 部分数据源可能需要代理访问美国网站，程序会自动检测常用代理。

### Q: 支持哪些ETF？
A: 支持所有在美国交易所上市的ETF，包括但不限于：
- SPY, QQQ, VTI, VOO (大盘指数)
- ARKK, ARKQ, ARKW (主题ETF)
- XLK, XLF, XLE (行业ETF)

### Q: 数据更新频率如何？
A: 数据来源于各大金融网站，通常为T+1更新。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

MIT License - 详见LICENSE文件

## 🎉 更新日志

### v2.0.0 (最新)
- ✅ 修复指定数据源时仍尝试其他数据源的bug
- ✅ 新增简化启动器 `run.py`，小白用户友好
- ✅ 完善Vanguard Selenium解析，支持ARKK、QQQ等更多ETF
- ✅ 改进Yahoo Finance解析准确性
- ✅ 增强错误处理和调试信息

### v1.0.0
- 🎯 基础功能实现
- 🌐 多数据源支持
- 📊 Excel批量处理
