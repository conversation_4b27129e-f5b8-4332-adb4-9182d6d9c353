# 📊 美股ETF持仓分析工具 - 使用说明

## 🎯 功能介绍

这是一个**一键式美股ETF持仓分析工具**，支持：
- 🔍 **单个美股ETF查询**：输入ETF代码，获取前十大持仓
- 📊 **批量Excel处理**：处理包含多个美股ETF的Excel文件
- 🌍 **多数据源支持**：Yahoo Finance、Morningstar等权威数据源

## 🚀 一键使用方法

### 方法1：交互式使用（推荐新手）
```bash
python3 us_etf_analyzer.py
```
然后按提示选择功能即可！

### 方法2：命令行使用

#### 查询单个美股ETF
```bash
python3 us_etf_analyzer.py SPY
python3 us_etf_analyzer.py QQQ
python3 us_etf_analyzer.py VTI
```

#### 处理Excel文件（所有工作表）
```bash
python3 us_etf_analyzer.py input/美股ETF列表.xlsx
```

#### 处理Excel文件（指定工作表）
```bash
python3 us_etf_analyzer.py input/美股ETF列表.xlsx 大盘ETF 科技ETF
```

## 📁 文件说明

### 核心文件
- **`us_etf_analyzer.py`** - 主程序，一键使用
- **`install.py`** - 一键安装脚本
- **`input/`** - 放置要处理的Excel文件
- **`使用说明.md`** - 本说明文件

### 输出文件
- **`us_etf_代码_holdings_时间.txt`** - 单个ETF查询结果
- **`文件名_updated.xlsx`** - 更新后的Excel文件（包含持仓数据）
- **`文件名_summary_时间.csv`** - CSV汇总文件

## 📋 Excel文件格式要求

Excel文件中只需要包含美股ETF代码（1-5位字母），程序会自动识别：

| ETF代码 | ETF名称 | 其他列... |
|---------|---------|-----------|
| SPY | SPDR S&P 500 ETF | ... |
| QQQ | Invesco QQQ Trust | ... |
| VTI | Vanguard Total Stock Market ETF | ... |

## 🎯 使用示例

### 示例1：查询单个美股ETF
```bash
$ python3 us_etf_analyzer.py SPY

正在查询美股ETF SPY...

✅ SPDR S&P 500 ETF Trust 前十大持仓:
--------------------------------------------------------------------------------
 1. MSFT     Microsoft Corporation                   7.12%
 2. AAPL     Apple Inc.                              6.89%
 3. NVDA     NVIDIA Corporation                      6.45%
 4. AMZN     Amazon.com Inc.                         3.21%
 5. GOOGL    Alphabet Inc. Class A                   2.98%
 6. GOOG     Alphabet Inc. Class C                   2.54%
 7. META     Meta Platforms Inc.                     2.31%
 8. TSLA     Tesla Inc.                              1.89%
 9. BRK.B    Berkshire Hathaway Inc. Class B         1.67%
10. UNH      UnitedHealth Group Inc.                 1.45%
--------------------------------------------------------------------------------
✅ 数据已保存到: us_etf_SPY_holdings_20250915_201234.txt
```

### 示例2：处理Excel文件
```bash
$ python3 us_etf_analyzer.py input/美股ETF列表.xlsx

正在处理Excel文件: input/美股ETF列表.xlsx
发现工作表: 大盘ETF, 科技ETF, 行业ETF, 国际ETF

📋 处理工作表: 大盘ETF
找到 8 个美股ETF代码
[1/8] 处理 SPY...
  ✅ 成功
[2/8] 处理 VOO...
  ✅ 成功
...

🎉 处理完成!
总计: 25 个美股ETF
成功: 22 个
失败: 3 个
成功率: 88.0%

✅ Excel文件已更新: input/美股ETF列表_updated.xlsx
✅ CSV汇总文件已生成: input/美股ETF列表_summary_20250915_201456.csv
```

## 🌍 支持的美股ETF类型

### 🏆 大盘指数ETF
- **SPY** - SPDR S&P 500 ETF（最大的S&P 500 ETF）
- **VOO** - Vanguard S&P 500 ETF（低费率）
- **IVV** - iShares Core S&P 500 ETF
- **VTI** - Vanguard Total Stock Market ETF（全市场）

### 💻 科技股ETF
- **QQQ** - Invesco QQQ Trust（纳斯达克100）
- **XLK** - Technology Select Sector SPDR Fund
- **VGT** - Vanguard Information Technology ETF
- **FTEC** - Fidelity MSCI Information Technology ETF

### 🏭 行业ETF
- **XLF** - Financial Select Sector SPDR Fund（金融）
- **XLE** - Energy Select Sector SPDR Fund（能源）
- **XLV** - Health Care Select Sector SPDR Fund（医疗）
- **XLI** - Industrial Select Sector SPDR Fund（工业）

### 🌐 国际ETF
- **EFA** - iShares MSCI EAFE ETF（发达市场）
- **VWO** - Vanguard Emerging Markets ETF（新兴市场）
- **VEA** - Vanguard FTSE Developed Markets ETF

## ❓ 常见问题

### Q: 为什么有些美股ETF获取失败？
A: 可能原因：
- 数据源暂时无法访问
- ETF代码错误或已退市
- 该ETF暂无持仓数据公开

### Q: 支持哪些文件格式？
A: 目前支持：
- Excel文件（.xlsx）
- 输出CSV文件（.csv）

### Q: 数据多久更新一次？
A: 数据来源于Yahoo Finance等公开数据源，通常是季度更新

### Q: 可以同时处理多个Excel文件吗？
A: 目前一次只能处理一个Excel文件，但支持多个工作表

### Q: 与AH_ETF工具有什么区别？
A: 
- **AH_ETF**: 专门处理A股和港股ETF，使用东方财富等中文数据源
- **U_ETF**: 专门处理美股ETF，使用Yahoo Finance等英文数据源
- 数据格式和解析逻辑针对不同市场优化

## 🔧 环境要求

需要安装以下Python包：
```bash
pip install requests beautifulsoup4 pandas openpyxl
```

## 📞 技术支持

如有问题，请检查：
1. 网络连接是否正常
2. 美股ETF代码是否正确（1-5位字母）
3. Excel文件格式是否正确

---

**🎉 现在就开始使用吧！只需要一行命令：`python3 us_etf_analyzer.py`**
