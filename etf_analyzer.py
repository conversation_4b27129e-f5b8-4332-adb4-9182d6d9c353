#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美股ETF持仓分析工具 - 一键版本
支持单个美股ETF查询和批量Excel文件处理
"""

import requests
from datetime import datetime
import re
from bs4 import BeautifulSoup
import sys
import os
import time
import pandas as pd
import traceback
import json

# 尝试导入yfinance，如果没有安装则跳过
try:
    import yfinance as yf
    YFINANCE_AVAILABLE = True
except ImportError:
    YFINANCE_AVAILABLE = False
    print("提示: 安装yfinance库可以提高数据获取成功率: pip install yfinance")

class USETFAnalyzer:
    def __init__(self, proxy=None, debug=False):
        self.debug = debug
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
        }

        # 设置代理
        self.proxy = proxy
        self.proxies = {}
        if proxy:
            self.proxies = {
                'http': proxy,
                'https': proxy
            }
            print(f"使用代理: {proxy}")

        # 创建session以保持cookies
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        if self.proxies:
            self.session.proxies.update(self.proxies)

    def is_valid_us_etf_symbol(self, symbol):
        """验证美股ETF代码是否有效"""
        if not symbol or len(symbol) < 1:
            return False

        # 美股ETF代码：1-5位字母，可能包含数字
        if 1 <= len(symbol) <= 5 and re.match(r'^[A-Za-z][A-Za-z0-9]*$', symbol):
            return True

        return False

    def validate_stock_symbol_online(self, symbol):
        """在线验证股票代码是否真实存在"""
        if not symbol or symbol in ['N/A', 'NAN', '--', 'YTD', 'ETF']:
            return False

        try:
            # 使用Yahoo Finance验证股票代码
            test_url = f"https://finance.yahoo.com/quote/{symbol}"
            response = self.session.get(test_url, timeout=10)

            # 如果页面包含股票信息，说明代码有效
            if response.status_code == 200:
                page_text = response.text.lower()
                # 检查是否包含股票相关信息
                valid_indicators = [
                    'quote-header', 'price', 'market cap', 'volume',
                    'previous close', 'open', 'bid', 'ask'
                ]

                if any(indicator in page_text for indicator in valid_indicators):
                    return True

            return False

        except Exception as e:
            if self.debug:
                print(f"  调试: 在线验证股票代码失败 {symbol}: {e}")
            return False

    def validate_stock_symbol(self, symbol):
        """验证股票代码是否真实存在"""
        if not symbol or symbol in ['N/A', 'NAN', '--', 'YTD', 'ETF']:
            return False, None

        try:
            # 清理股票代码
            clean_symbol = symbol.strip().upper()

            # 过滤明显无效的代码
            invalid_codes = [
                'YTD', 'ETF', 'SECTOR', 'TOTAL', 'ASSETS', 'PORTFOLIO',
                'GOVERNMENT', 'BOND', 'CASH', 'OTHER', 'UNKNOWN'
            ]

            if clean_symbol in invalid_codes:
                return False, None

            # 过滤太短的代码（除了一些特殊情况）
            if len(clean_symbol) <= 2 and clean_symbol not in ['GO', 'IT', 'AI', 'ON']:
                # 这些可能是错误解析的结果，但不要过滤国际交易所后缀
                # 只有当它们单独出现时才过滤，如果是完整格式如SAP.DE则不过滤
                if clean_symbol in ['DE', 'AS', 'KS', 'SW', 'AX', 'TO'] and '.' not in symbol:
                    return False, None

            # 特殊情况处理
            special_mappings = {
                'SHOP.TO': 'SHOP',  # Shopify在多伦多交易所，但美股代码是SHOP
                'TO': 'SHOP',       # 如果只提取到TO，很可能是SHOP.TO的错误解析
            }

            if clean_symbol in special_mappings:
                return True, special_mappings[clean_symbol]

            # 处理特殊格式的股票代码
            if '.' in clean_symbol:
                # 国际股票代码格式，如 SAP.DE, ASML.AS, 005930.KS
                parts = clean_symbol.split('.')
                if len(parts) == 2:
                    base_symbol, exchange = parts
                    if len(base_symbol) >= 2:
                        # 对于SHOP.TO这种情况，返回基础代码SHOP
                        if clean_symbol == 'SHOP.TO':
                            return True, 'SHOP'
                        # 保留完整的国际代码
                        return True, clean_symbol
            elif '-' in clean_symbol:
                # 如 BRK-B, BRK-A
                if len(clean_symbol) >= 3:
                    return True, clean_symbol
            elif len(clean_symbol) >= 1 and len(clean_symbol) <= 6:
                # 标准美股代码
                if re.match(r'^[A-Z][A-Z0-9]*$', clean_symbol):
                    return True, clean_symbol

            return False, None

        except Exception as e:
            if self.debug:
                print(f"  调试: 股票代码验证失败 {symbol}: {e}")
            return False, None

    def extract_and_validate_symbol(self, raw_symbol, company_name=""):
        """从原始数据中提取并验证股票代码"""
        if not raw_symbol:
            return None

        # 尝试直接验证原始代码
        is_valid, clean_symbol = self.validate_stock_symbol(raw_symbol)
        if is_valid:
            return clean_symbol

        # 如果原始代码无效，尝试从公司名称中提取
        if company_name:
            # 常见的股票代码映射
            symbol_mapping = {
                # 美股
                'Tesla': 'TSLA', 'Tesla Inc': 'TSLA', 'Tesla, Inc.': 'TSLA',
                'NVIDIA': 'NVDA', 'NVIDIA Corp': 'NVDA', 'NVIDIA Corporation': 'NVDA',
                'Microsoft': 'MSFT', 'Microsoft Corp': 'MSFT', 'Microsoft Corporation': 'MSFT',
                'Apple': 'AAPL', 'Apple Inc': 'AAPL', 'Apple Inc.': 'AAPL',
                'Amazon': 'AMZN', 'Amazon.com': 'AMZN', 'Amazon.com, Inc.': 'AMZN',
                'Meta Platforms': 'META', 'Meta Platforms, Inc.': 'META',
                'Alphabet': 'GOOGL', 'Alphabet Inc': 'GOOGL', 'Alphabet Inc.': 'GOOGL',
                'Berkshire Hathaway': 'BRK-B', 'Berkshire Hathaway Inc': 'BRK-B',
                'Broadcom': 'AVGO', 'Broadcom Inc': 'AVGO', 'Broadcom Inc.': 'AVGO',
                'Netflix': 'NFLX', 'Netflix, Inc.': 'NFLX', 'Netflix Inc': 'NFLX',
                'Shopify': 'SHOP', 'Shopify Inc': 'SHOP',
                'Roku': 'ROKU', 'Roku, Inc.': 'ROKU', 'Roku Inc': 'ROKU',
                'Coinbase': 'COIN', 'Coinbase Global': 'COIN', 'Coinbase Global, Inc.': 'COIN',
                'Palantir': 'PLTR', 'Palantir Technologies': 'PLTR',
                'AMD': 'AMD', 'Advanced Micro Devices': 'AMD',
                'CRISPR': 'CRSP', 'CRISPR Therapeutics': 'CRSP',
                'Robinhood': 'HOOD', 'Robinhood Markets': 'HOOD',
                'Roblox': 'RBLX', 'Roblox Corporation': 'RBLX',
                'Tempus AI': 'TEM', 'Tempus AI, Inc.': 'TEM',

                # 国际股票
                'SAP SE': 'SAP.DE', 'SAP': 'SAP.DE',
                'ASML Holding N.V.': 'ASML.AS', 'ASML Holding': 'ASML.AS', 'ASML': 'ASML.AS',
                'Samsung Electronics Co., Ltd.': '005930.KS', 'Samsung Electronics': '005930.KS', 'Samsung': '005930.KS',
                'Nestlé S.A.': 'NESN.SW', 'Nestle SA': 'NESN.SW', 'Nestle': 'NESN.SW',
                'AstraZeneca PLC': 'AZN.L', 'AstraZeneca plc': 'AZN.L', 'AstraZeneca': 'AZN.L',
                'Roche Holding AG': 'ROG.SW', 'Roche Holding': 'ROG.SW', 'Roche': 'ROG.SW',
                'Novartis AG': 'NOVN.SW', 'Novartis AG Registered Shares': 'NOVN.SW', 'Novartis': 'NOVN.SW',
                'HSBC Holdings plc': 'HSBA.L', 'HSBC Holdings PLC': 'HSBA.L', 'HSBC Holdings': 'HSBA.L', 'HSBC': 'HSBA.L',
                'Shell plc': 'SHEL.L', 'Shell PLC': 'SHEL.L', 'Shell': 'SHEL.L',
                'Siemens AG': 'SIE.DE', 'Siemens': 'SIE.DE',
                'Commonwealth Bank of Australia': 'CBA.AX', 'Commonwealth Bank': 'CBA.AX'
            }

            for name_pattern, symbol in symbol_mapping.items():
                if name_pattern.lower() in company_name.lower():
                    if self.debug:
                        print(f"  调试: 从公司名称'{company_name}'映射到股票代码'{symbol}'")
                    return symbol

        if self.debug:
            print(f"  调试: 无法验证股票代码'{raw_symbol}'，公司名称'{company_name}'")
        return None

    def get_us_etf_holdings_yahoo(self, symbol):
        """从Yahoo Finance获取美股ETF持仓数据"""
        try:
            # Yahoo Finance ETF holdings URL
            url = f"https://finance.yahoo.com/quote/{symbol}/holdings"

            print(f"  尝试从Yahoo Finance获取数据: {url}")
            response = self.session.get(url, timeout=15)
            response.encoding = 'utf-8'

            print(f"  响应状态码: {response.status_code}")
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')

                # 调试模式：保存HTML内容
                if self.debug:
                    debug_file = f"debug_yahoo_{symbol}.html"
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    print(f"  调试: HTML内容已保存到 {debug_file}")

                holdings = self.parse_yahoo_holdings(soup)
                if holdings:
                    print(f"  成功解析到 {len(holdings)} 个持仓")
                    return holdings
                else:
                    print("  未能解析到持仓数据")
                    if self.debug:
                        print(f"  调试: 页面标题: {soup.title.string if soup.title else 'N/A'}")
                        tables = soup.find_all('table')
                        print(f"  调试: 找到 {len(tables)} 个表格")

            return None

        except Exception as e:
            print(f"从Yahoo Finance获取数据失败: {e}")
            return None

    def parse_yahoo_holdings(self, soup):
        """解析Yahoo Finance的持仓数据"""
        try:
            holdings = []

            # 方法1: 查找持仓表格
            tables = soup.find_all('table')

            for table in tables:
                rows = table.find_all('tr')
                if len(rows) > 1:
                    # 检查是否是持仓表格
                    header_row = rows[0]
                    header_cells = header_row.find_all(['th', 'td'])
                    header_text = ' '.join([cell.get_text().strip().lower() for cell in header_cells])

                    if any(keyword in header_text for keyword in ['symbol', 'holding', 'weight', '% assets', 'company']):
                        # 找到列位置
                        symbol_col = name_col = weight_col = -1
                        for i, cell in enumerate(header_cells):
                            cell_text = cell.get_text().strip().lower()
                            if 'symbol' in cell_text or 'ticker' in cell_text:
                                symbol_col = i
                            elif 'holding' in cell_text or 'name' in cell_text or 'company' in cell_text:
                                name_col = i
                            elif 'weight' in cell_text or '% assets' in cell_text or 'allocation' in cell_text:
                                weight_col = i

                        # 解析数据行
                        for i, row in enumerate(rows[1:11]):  # 前10行
                            cells = row.find_all('td')
                            if len(cells) >= 2:
                                symbol = ""
                                name = ""
                                weight = ""

                                # 按列位置提取数据
                                if symbol_col >= 0 and symbol_col < len(cells):
                                    symbol = cells[symbol_col].get_text().strip()
                                if name_col >= 0 and name_col < len(cells):
                                    name = cells[name_col].get_text().strip()
                                if weight_col >= 0 and weight_col < len(cells):
                                    weight = cells[weight_col].get_text().strip()

                                # 如果没找到明确列位置，尝试不同的组合
                                if not name:
                                    for cell in cells:
                                        cell_text = cell.get_text().strip()
                                        # 查找看起来像公司名称的文本
                                        if (len(cell_text) > 5 and
                                            not cell_text.replace('.', '').replace('%', '').replace(',', '').isdigit() and
                                            not re.match(r'^[A-Z]{1,5}$', cell_text)):
                                            name = cell_text
                                            break

                                # 查找权重
                                if not weight:
                                    for cell in cells:
                                        cell_text = cell.get_text().strip()
                                        if '%' in cell_text and re.search(r'\d+\.?\d*%', cell_text):
                                            weight = cell_text
                                            break

                                # 清理数据
                                if weight:
                                    weight_match = re.search(r'(\d+\.?\d*%)', weight)
                                    if weight_match:
                                        weight = weight_match.group(1)

                                # 验证并添加数据
                                if name and len(name) > 2 and name not in ['Total', 'Other', 'Cash', 'Others']:
                                    holdings.append({
                                        'rank': i + 1,
                                        'symbol': symbol if symbol else 'N/A',
                                        'name': name,
                                        'weight': weight if weight else '--'
                                    })

                        if holdings:
                            return holdings

            # 方法2: 查找特定的CSS选择器（Yahoo Finance可能使用的）
            holding_divs = soup.find_all('div', class_=re.compile(r'holding|portfolio', re.I))
            for div in holding_divs:
                # 在div中查找数据
                text_content = div.get_text()
                if 'holdings' in text_content.lower() or 'portfolio' in text_content.lower():
                    # 尝试提取结构化数据
                    sub_divs = div.find_all('div')
                    for i, sub_div in enumerate(sub_divs[:10]):
                        text = sub_div.get_text().strip()
                        if len(text) > 5 and '%' in text:
                            # 尝试分离名称和权重
                            parts = text.split()
                            if len(parts) >= 2:
                                weight = next((p for p in parts if '%' in p), '')
                                name = text.replace(weight, '').strip()
                                if name:
                                    holdings.append({
                                        'rank': i + 1,
                                        'symbol': 'N/A',
                                        'name': name,
                                        'weight': weight
                                    })

                if holdings:
                    return holdings

            # 方法3: 从页面文本中解析持仓数据
            holdings = self.parse_holdings_from_page_text(soup)
            if holdings:
                return holdings

            return None

        except Exception as e:
            print(f"解析Yahoo Finance数据失败: {e}")
            return None

    def parse_holdings_from_page_text(self, soup):
        """从页面文本中解析持仓数据"""
        try:
            holdings = []
            page_text = soup.get_text()

            if self.debug:
                print(f"  调试: 页面文本长度: {len(page_text)}")
                # 寻找包含持仓信息的部分
                if 'Top 10 Holdings' in page_text:
                    start_idx = page_text.find('Top 10 Holdings')
                    end_idx = start_idx + 2000
                    print(f"  调试: 持仓部分文本: {page_text[start_idx:end_idx]}")

            # 已知的热门股票代码
            known_stocks = ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'GOOG', 'AVGO', 'TSLA', 'BRK-B', 'ORCL', 'LLY', 'V', 'JPM', 'UNH']

            # 首先尝试从Top 10 Holdings部分精确提取
            if 'Top 10 Holdings' in page_text:
                start_idx = page_text.find('Top 10 Holdings')
                # 找到持仓部分的结束位置（通常是"Get Quotes"或"Sector"）
                end_markers = ['Get Quotes', 'Sector Weightings', 'Overall Portfolio']
                end_idx = len(page_text)
                for marker in end_markers:
                    marker_pos = page_text.find(marker, start_idx)
                    if marker_pos != -1:
                        end_idx = min(end_idx, marker_pos)

                holdings_text = page_text[start_idx:end_idx]
                if self.debug:
                    print(f"  调试: 持仓文本段: {holdings_text}")

                # 使用更精确的模式匹配持仓数据
                # 匹配格式: SYMBOL     Company Name Inc. X.XX%
                # 支持 SHOP.TO 这种格式，但提取主要部分
                # 支持国际公司后缀：Inc., Corporation, Company, Ltd., PLC, plc, AG, SE, N.V., S.A.
                pattern = r'([A-Z0-9-\.]{2,12})\s+([^0-9\n]+?(?:Inc\.|Corporation|Company|Ltd\.|PLC|plc|AG|SE|N\.V\.|S\.A\.|Co\.|Group)[^0-9\n]*?)\s+(\d+\.\d+%)'
                matches = re.findall(pattern, holdings_text, re.MULTILINE)

                for match in matches:
                    symbol, name, weight = match
                    symbol = symbol.strip()
                    name = name.strip()
                    weight = weight.strip()

                    # 验证并清理股票代码
                    validated_symbol = self.extract_and_validate_symbol(symbol, name)

                    # 过滤掉无效的匹配
                    if (len(holdings) < 10 and
                        validated_symbol and
                        symbol not in ['TOP', 'SYMBOL', 'ASSETS', 'US'] and
                        len(symbol) <= 6 and
                        'Government' not in name and
                        'Sector' not in name):
                        holdings.append({
                            'rank': len(holdings) + 1,
                            'symbol': validated_symbol,  # 使用验证后的代码
                            'name': name,
                            'weight': weight
                        })
                        if self.debug:
                            if validated_symbol != symbol:
                                print(f"  调试: 从文本解析到: {symbol} -> {validated_symbol} - {name} ({weight})")
                            else:
                                print(f"  调试: 从文本解析到: {validated_symbol} - {name} ({weight})")

            # 如果精确匹配没有找到足够数据，使用备用模式
            if len(holdings) < 10:
                # 模式1: 寻找 "股票代码 公司名 百分比" 的模式
                patterns = [
                    # 国际股票代码格式，如 SAP.DE, ASML.AS, 005930.KS
                    r'([A-Z0-9]+\.[A-Z]{2,3})\s+([^0-9\n]+?)\s+(\d+\.\d+%)',
                    # 标准格式，支持BRK-B
                    r'([A-Z-]{2,6})\s+([^0-9\n]+?)\s+(\d+\.\d+%)',
                    # 多空格格式
                    r'([A-Z-]{2,6})\s{5,}([^0-9\n]+?)\s+(\d+\.\d+%)',
                ]

                for pattern in patterns:
                    matches = re.findall(pattern, page_text, re.MULTILINE)

                    for match in matches:
                        symbol, name, weight = match
                        symbol = symbol.strip()
                        name = name.strip()
                        weight = weight.strip()

                        # 验证并清理股票代码
                        validated_symbol = self.extract_and_validate_symbol(symbol, name)

                        # 验证是否是已知的股票代码或者是合理的股票代码格式
                        # 过滤掉ETF自身和行业分类
                        if (validated_symbol and
                            len(holdings) < 10 and
                            symbol not in ['TOP', 'SYMBOL', 'ASSETS', 'US'] and
                            validated_symbol not in ['VEA', 'VTI', 'VOO', 'SPY', 'QQQ', 'IVV', 'VUG', 'IEFA'] and  # 过滤ETF自身
                            name not in ['Energy', 'Technology', 'Healthcare', 'Financial', 'Consumer', 'Government', 'Financial Services', 'Daily Total Return'] and
                            'Government' not in name and
                            'Total Return' not in name and
                            not (len(name.split()) == 1 and name in ['Energy', 'Technology', 'Healthcare'])):
                            # 检查是否已经添加过这个股票
                            if not any(h['symbol'] == validated_symbol for h in holdings):
                                # 对于国际股票代码，进行在线验证（可选）
                                is_valid = True
                                if self.debug and '.' in validated_symbol:
                                    is_valid = self.validate_stock_symbol_online(validated_symbol)
                                    if not is_valid:
                                        print(f"  调试: 在线验证失败: {validated_symbol}")

                                if is_valid:
                                    holdings.append({
                                        'rank': len(holdings) + 1,
                                        'symbol': validated_symbol,  # 使用验证后的代码
                                        'name': name,
                                        'weight': weight
                                    })
                                    if self.debug:
                                        if validated_symbol != symbol:
                                            print(f"  调试: 从备用模式解析到: {symbol} -> {validated_symbol} - {name} ({weight})")
                                        else:
                                            print(f"  调试: 从备用模式解析到: {validated_symbol} - {name} ({weight})")

                    if len(holdings) >= 10:
                        break

            # 模式2: 如果模式1没找到足够数据，尝试分行解析
            if len(holdings) < 5:
                lines = page_text.split('\n')
                for i, line in enumerate(lines):
                    line = line.strip()
                    for stock in known_stocks:
                        if line == stock or line.startswith(stock + ' '):
                            # 在附近的行中寻找公司名和权重
                            name = "Unknown Company"
                            weight = "N/A"

                            # 向下寻找公司名和权重
                            for j in range(i + 1, min(len(lines), i + 5)):
                                next_line = lines[j].strip()
                                if ('Corporation' in next_line or 'Inc.' in next_line or
                                    'Company' in next_line or 'Ltd.' in next_line):
                                    name = next_line
                                elif '%' in next_line and re.search(r'\d+\.\d+%', next_line):
                                    weight_match = re.search(r'(\d+\.\d+%)', next_line)
                                    if weight_match:
                                        weight = weight_match.group(1)

                            # 检查是否已经添加过这个股票
                            if not any(h['symbol'] == stock for h in holdings):
                                holdings.append({
                                    'rank': len(holdings) + 1,
                                    'symbol': stock,
                                    'name': name,
                                    'weight': weight
                                })
                                if self.debug:
                                    print(f"  调试: 从分行解析到: {stock} - {name} ({weight})")

                                if len(holdings) >= 10:
                                    break

                    if len(holdings) >= 10:
                        break

            return holdings if holdings else None

        except Exception as e:
            if self.debug:
                print(f"  调试: 文本解析出错: {e}")
            return None

    def get_us_etf_holdings_morningstar(self, symbol):
        """从Morningstar获取美股ETF持仓数据"""
        try:
            # Morningstar ETF holdings URL
            urls = [
                f"https://www.morningstar.com/etfs/{symbol}/portfolio",
                f"https://www.morningstar.com/etfs/{symbol.lower()}/portfolio",
                f"https://www.morningstar.com/funds/{symbol}/portfolio",
                f"https://www.morningstar.com/etfs/xnas/{symbol}/portfolio",
            ]

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            for url in urls:
                try:
                    print(f"  尝试从Morningstar获取数据: {url}")
                    response = self.session.get(url, timeout=20, headers=headers)
                    response.encoding = 'utf-8'

                    print(f"  响应状态码: {response.status_code}")
                    if response.status_code == 200:
                        if self.debug:
                            with open(f'debug_morningstar_{symbol}.html', 'w', encoding='utf-8') as f:
                                f.write(response.text)
                            print(f"  调试: Morningstar HTML内容已保存到 debug_morningstar_{symbol}.html")

                        soup = BeautifulSoup(response.text, 'html.parser')
                        holdings = self.parse_morningstar_holdings(soup)
                        if holdings:
                            print(f"  成功解析到 {len(holdings)} 个持仓")
                            return holdings
                        else:
                            print("  未能解析到持仓数据")

                except Exception as e:
                    if self.debug:
                        print(f"  调试: Morningstar URL {url} 失败: {e}")
                    continue

            return None

        except Exception as e:
            print(f"从Morningstar获取数据失败: {e}")
            return None

    def parse_morningstar_holdings(self, soup):
        """解析Morningstar的持仓数据"""
        try:
            holdings = []

            if self.debug:
                print(f"  调试: Morningstar页面标题: {soup.title.string if soup.title else 'N/A'}")

            # 方法1: 查找持仓数据 - Morningstar通常使用特定的CSS类名
            holding_rows = soup.find_all('tr', class_=re.compile(r'holding|portfolio'))

            if holding_rows:
                if self.debug:
                    print(f"  调试: 找到 {len(holding_rows)} 个持仓行")
            else:
                # 尝试查找表格
                tables = soup.find_all('table')
                for table in tables:
                    rows = table.find_all('tr')
                    if len(rows) > 1:
                        header_row = rows[0]
                        header_text = header_row.get_text().lower()

                        if self.debug:
                            print(f"  调试: Morningstar表格头: {header_text}")

                        if any(keyword in header_text for keyword in ['holding', 'weight', 'assets', 'portfolio']):
                            holding_rows = rows[1:11]  # 前10行
                            if self.debug:
                                print(f"  调试: 找到持仓表格，有 {len(holding_rows)} 行")
                            break

            for i, row in enumerate(holding_rows[:10]):
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    symbol = ""
                    name = cells[0].get_text().strip()
                    weight = ""

                    # 查找股票代码和权重
                    for j, cell in enumerate(cells):
                        cell_text = cell.get_text().strip()
                        if j == 0 and re.match(r'^[A-Z]{2,6}$', cell_text):
                            symbol = cell_text
                        elif j == 1 and not symbol:
                            name = cell_text
                        elif '%' in cell_text:
                            weight = cell_text
                            break

                    if name and name not in ['Total', 'Other', 'Cash', 'Symbol', 'Holding']:
                        holdings.append({
                            'rank': i + 1,
                            'symbol': symbol if symbol else 'N/A',
                            'name': name,
                            'weight': weight if weight else '--'
                        })
                        if self.debug:
                            print(f"  调试: Morningstar解析到: {symbol} - {name} ({weight})")

            # 方法2: 从页面文本中解析
            if not holdings:
                holdings = self.parse_morningstar_text(soup)

            return holdings if holdings else None

        except Exception as e:
            if self.debug:
                print(f"  调试: Morningstar解析出错: {e}")
            return None

    def parse_morningstar_text(self, soup):
        """从Morningstar页面文本中解析持仓数据"""
        try:
            holdings = []
            page_text = soup.get_text()

            if self.debug:
                print(f"  调试: Morningstar页面文本长度: {len(page_text)}")

            # 已知的热门股票代码
            known_stocks = ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'GOOG', 'AVGO', 'TSLA', 'BRK-B', 'ORCL', 'LLY', 'V', 'JPM', 'UNH']

            # 寻找股票代码和权重的模式
            import re
            pattern = r'([A-Z]{2,6})\s+([^0-9\n]+?)\s+(\d+\.\d+%)'
            matches = re.findall(pattern, page_text, re.MULTILINE)

            for match in matches:
                symbol, name, weight = match
                symbol = symbol.strip()
                name = name.strip()
                weight = weight.strip()

                if symbol in known_stocks and len(holdings) < 10:
                    holdings.append({
                        'rank': len(holdings) + 1,
                        'symbol': symbol,
                        'name': name,
                        'weight': weight
                    })
                    if self.debug:
                        print(f"  调试: Morningstar文本解析到: {symbol} - {name} ({weight})")

            return holdings if holdings else None

        except Exception as e:
            if self.debug:
                print(f"  调试: Morningstar文本解析出错: {e}")
            return None

    def get_vanguard_holdings(self, symbol):
        """从Vanguard官网获取ETF持仓数据"""
        try:
            # Vanguard ETF页面URL - 使用小写
            urls = [
                f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}",
                f"https://investor.vanguard.com/etf/profile/{symbol}",
                f"https://investor.vanguard.com/etf/profile/{symbol.upper()}",
            ]

            for url in urls:
                try:
                    print(f"  尝试从Vanguard官网获取数据: {url}")
                    response = self.session.get(url, timeout=20, allow_redirects=True)
                    response.encoding = 'utf-8'

                    print(f"  响应状态码: {response.status_code}")
                    if response.status_code == 200:
                        if self.debug:
                            print(f"  调试: Vanguard最终URL: {response.url}")
                            with open(f'debug_vanguard_{symbol}.html', 'w', encoding='utf-8') as f:
                                f.write(response.text)
                            print(f"  调试: Vanguard HTML内容已保存到 debug_vanguard_{symbol}.html")

                        soup = BeautifulSoup(response.text, 'html.parser')
                        holdings = self.parse_vanguard_holdings(soup)
                        if holdings:
                            print(f"  成功解析到 {len(holdings)} 个持仓")
                            return holdings
                        else:
                            print("  未能解析到持仓数据")

                except Exception as e:
                    if self.debug:
                        print(f"  调试: Vanguard URL {url} 失败: {e}")
                    continue

            return None

        except Exception as e:
            print(f"从Vanguard官网获取数据失败: {e}")
            return None

    def get_vanguard_selenium_final(self, symbol):
        """使用Selenium从Vanguard官网获取持仓数据 - 最终版本"""
        try:
            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.chrome.service import Service
            from webdriver_manager.chrome import ChromeDriverManager
            import time

            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # 如果有代理，添加代理设置
            if self.proxies and 'http' in self.proxies:
                chrome_options.add_argument(f'--proxy-server={self.proxies["http"]}')

            # 启动WebDriver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(60)

            try:
                # 访问基金主页
                url = f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}"
                if self.debug:
                    print(f"  调试: 访问Vanguard URL: {url}")

                start_time = time.time()
                driver.get(url)
                time.sleep(3)  # 减少等待时间

                # 寻找并点击Portfolio composition标签
                try:
                    portfolio_element = WebDriverWait(driver, 8).until(
                        EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Portfolio composition')]"))
                    )
                    portfolio_element.click()
                    if self.debug:
                        print("  调试: 成功点击Portfolio composition标签")
                    time.sleep(5)  # 减少等待数据加载时间
                except:
                    if self.debug:
                        print("  调试: 未找到Portfolio composition标签，尝试直接访问持仓页面")
                    holdings_url = f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}/portfolio-composition"
                    driver.get(holdings_url)
                    time.sleep(6)  # 减少等待时间

                # 等待持仓数据加载
                time.sleep(5)  # 减少等待时间

                # 获取页面源码
                page_source = driver.page_source

                if self.debug:
                    with open(f'debug_vanguard_selenium_{symbol}.html', 'w', encoding='utf-8') as f:
                        f.write(page_source)
                    print(f"  调试: 已保存 debug_vanguard_selenium_{symbol}.html")

                # 解析持仓数据
                soup = BeautifulSoup(page_source, 'html.parser')
                holdings = []

                # 使用集合来避免重复
                seen_symbols = set()

                # 策略1: 查找持仓表格中的数据行
                try:
                    # 查找所有表格行
                    all_rows = soup.find_all('tr')

                    for row in all_rows:
                        try:
                            # 查找包含股票代码的单元格
                            symbol_cells = row.find_all(attrs={"data-rpa-tag-id": "symbol"})
                            if not symbol_cells:
                                continue

                            symbol_text = symbol_cells[0].get_text().strip()

                            # 验证股票代码格式，允许更多格式，但排除ETF本身
                            if not re.match(r'^[A-Z]{1,6}(\.[A-Z])?$', symbol_text):
                                continue

                            # 排除ETF本身
                            if symbol_text.upper() == symbol.upper():
                                continue

                            # 避免重复
                            if symbol_text in seen_symbols:
                                continue
                            seen_symbols.add(symbol_text)

                            # 查找公司名称 - 尝试多种可能的标签
                            name_text = ""
                            name_selectors = [
                                {"data-rpa-tag-id": "holdings"},
                                {"data-rpa-tag-id": "companyName"},
                                {"data-rpa-tag-id": "holdingName"}
                            ]

                            for selector in name_selectors:
                                name_elem = row.find(attrs=selector)
                                if name_elem:
                                    name_text = name_elem.get_text().strip()
                                    break

                            # 如果没找到，尝试从相邻单元格获取
                            if not name_text:
                                cells = row.find_all(['td', 'mat-cell'])
                                if len(cells) >= 2:
                                    name_text = cells[1].get_text().strip()

                            # 查找权重 - 尝试多种可能的标签
                            weight_text = ""
                            weight_selectors = [
                                {"data-rpa-tag-id": "fundPercent"},
                                {"data-rpa-tag-id": "pctOfFund"},
                                {"data-rpa-tag-id": "weight"}
                            ]

                            for selector in weight_selectors:
                                weight_elem = row.find(attrs=selector)
                                if weight_elem:
                                    weight_text = weight_elem.get_text().strip()
                                    break

                            # 如果没找到，尝试从包含%的单元格获取
                            if not weight_text:
                                cells = row.find_all(['td', 'mat-cell'])
                                for cell in cells:
                                    cell_text = cell.get_text().strip()
                                    if '%' in cell_text and re.search(r'\d+\.\d+\s*%', cell_text):
                                        weight_text = cell_text
                                        break

                            # 清理权重格式
                            if weight_text:
                                weight_match = re.search(r'([\d.]+)\s*%', weight_text)
                                if weight_match:
                                    weight_text = f"{weight_match.group(1)} %"

                            if symbol_text and name_text:
                                holdings.append({
                                    'rank': len(holdings) + 1,
                                    'symbol': symbol_text,
                                    'name': name_text,
                                    'weight': weight_text or "N/A"
                                })

                                if self.debug:
                                    print(f"  调试: 解析到持仓: {symbol_text} - {name_text} ({weight_text})")

                                if len(holdings) >= 10:
                                    break

                        except Exception as e:
                            if self.debug:
                                print(f"  调试: 解析行数据异常: {e}")
                            continue

                except Exception as e:
                    if self.debug:
                        print(f"  调试: 表格解析失败: {e}")

                # 策略2: 查找"Month end 10 largest holdings"表格（适用于ARKK等ETF）
                if len(holdings) < 5:
                    if self.debug:
                        print("  调试: 策略1结果不足，尝试策略2 - 查找Month end holdings表格")

                    # 查找包含"Top ten monthend holdings"或"Month end 10 largest holdings"的表格
                    tables = soup.find_all('table')
                    for table in tables:
                        # 检查表格是否包含持仓数据的标识
                        table_text = table.get_text().lower()
                        if any(keyword in table_text for keyword in ['holdings', 'rank', '% of fund']):
                            rows = table.find_all('tr')
                            for row in rows:
                                cells = row.find_all(['td', 'th'])
                                if len(cells) >= 3:
                                    try:
                                        # 第一列通常是排名
                                        rank_text = cells[0].get_text().strip()
                                        if not rank_text.isdigit():
                                            continue

                                        # 第二列是公司名称
                                        name_text = cells[1].get_text().strip()
                                        if not name_text or len(name_text) < 3:
                                            continue

                                        # 第三列是权重百分比
                                        weight_text = cells[2].get_text().strip()
                                        if '%' not in weight_text:
                                            continue

                                        # 尝试从公司名称中提取股票代码
                                        symbol_text = self.extract_symbol_from_name(name_text)

                                        if symbol_text and symbol_text not in seen_symbols:
                                            seen_symbols.add(symbol_text)
                                            holdings.append({
                                                'rank': len(holdings) + 1,
                                                'symbol': symbol_text,
                                                'name': name_text,
                                                'weight': weight_text
                                            })

                                            if self.debug:
                                                print(f"  调试: 策略2解析到持仓: {symbol_text} - {name_text} ({weight_text})")

                                            if len(holdings) >= 10:
                                                break

                                    except Exception as e:
                                        if self.debug:
                                            print(f"  调试: 策略2解析行失败: {e}")
                                        continue

                            if len(holdings) >= 5:
                                break

                # 策略3: 使用原有方法作为最后备用
                if len(holdings) < 5:
                    if self.debug:
                        print("  调试: 策略2结果不足，尝试策略3 - 原有方法")

                    symbol_elements = soup.find_all(attrs={"data-rpa-tag-id": "symbol"})

                    for symbol_elem in symbol_elements:
                        try:
                            symbol_text = symbol_elem.get_text().strip()
                            if not re.match(r'^[A-Z]{1,6}(\.[A-Z])?$', symbol_text):
                                continue

                            # 排除ETF本身
                            if symbol_text.upper() == symbol.upper():
                                continue

                            if symbol_text in seen_symbols:
                                continue
                            seen_symbols.add(symbol_text)

                            row = symbol_elem.find_parent('tr')
                            if row:
                                name_elem = row.find(attrs={"data-rpa-tag-id": "holdings"})
                                name_text = name_elem.get_text().strip() if name_elem else ""

                                weight_elem = row.find(attrs={"data-rpa-tag-id": "fundPercent"})
                                weight_text = weight_elem.get_text().strip() if weight_elem else ""

                                if symbol_text and name_text:
                                    holdings.append({
                                        'rank': len(holdings) + 1,
                                        'symbol': symbol_text,
                                        'name': name_text,
                                        'weight': weight_text or "N/A"
                                    })

                                    if self.debug:
                                        print(f"  调试: 策略3解析到持仓: {symbol_text} - {name_text} ({weight_text})")

                                    if len(holdings) >= 10:
                                        break
                        except Exception as e:
                            continue

                if self.debug:
                    elapsed = time.time() - start_time
                    print(f"  调试: Selenium处理耗时: {elapsed:.2f}秒")

                return holdings if holdings else None

            finally:
                driver.quit()

        except ImportError:
            if self.debug:
                print("  调试: Selenium未安装，跳过Vanguard Selenium方法")
            return None
        except Exception as e:
            if self.debug:
                print(f"  调试: Vanguard Selenium获取异常: {e}")
            return None

    def parse_vanguard_holdings(self, soup):
        """解析Vanguard官网的持仓数据"""
        try:
            holdings = []

            # 查找持仓表格 - Vanguard通常使用特定的CSS类名
            tables = soup.find_all('table')

            for table in tables:
                rows = table.find_all('tr')
                if len(rows) > 1:
                    header_row = rows[0]
                    header_cells = header_row.find_all(['th', 'td'])
                    header_text = ' '.join([cell.get_text().strip().lower() for cell in header_cells])

                    # 查找包含持仓信息的表格
                    if any(keyword in header_text for keyword in ['holding', 'security', 'weight', 'allocation', '% of']):
                        # 找到列位置
                        symbol_col = name_col = weight_col = -1
                        for i, cell in enumerate(header_cells):
                            cell_text = cell.get_text().strip().lower()
                            if 'symbol' in cell_text or 'ticker' in cell_text:
                                symbol_col = i
                            elif 'holding' in cell_text or 'security' in cell_text or 'name' in cell_text:
                                name_col = i
                            elif 'weight' in cell_text or '% of' in cell_text or 'allocation' in cell_text:
                                weight_col = i

                        # 解析数据行
                        for i, row in enumerate(rows[1:11]):  # 前10行
                            cells = row.find_all('td')
                            if len(cells) >= 2:
                                symbol = ""
                                name = ""
                                weight = ""

                                # 按列位置提取数据
                                if symbol_col >= 0 and symbol_col < len(cells):
                                    symbol = cells[symbol_col].get_text().strip()
                                if name_col >= 0 and name_col < len(cells):
                                    name = cells[name_col].get_text().strip()
                                if weight_col >= 0 and weight_col < len(cells):
                                    weight = cells[weight_col].get_text().strip()

                                # 如果没找到明确列位置，按常见格式解析
                                if not name and len(cells) >= 2:
                                    name = cells[0].get_text().strip()
                                    if len(cells) >= 2:
                                        weight = cells[1].get_text().strip()

                                # 清理数据
                                if weight:
                                    weight_match = re.search(r'(\d+\.?\d*%)', weight)
                                    if weight_match:
                                        weight = weight_match.group(1)

                                # 验证并添加数据
                                if name and name not in ['Total', 'Other', 'Cash', 'Others']:
                                    holdings.append({
                                        'rank': i + 1,
                                        'symbol': symbol if symbol else 'N/A',
                                        'name': name,
                                        'weight': weight if weight else '--'
                                    })

                        if holdings:
                            return holdings

            return None

        except Exception as e:
            print(f"解析Vanguard数据失败: {e}")
            return None

    def get_etf_com_holdings(self, symbol):
        """从ETF.com获取ETF持仓数据"""
        try:
            # ETF.com ETF页面URL
            urls = [
                f"https://www.etf.com/{symbol}",
                f"https://www.etf.com/{symbol}/holdings",
                f"https://www.etf.com/funds/{symbol}",
            ]

            # 更强的反爬虫绕过
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
            }

            for url in urls:
                try:
                    print(f"  尝试从ETF.com获取数据: {url}")

                    # 创建新的session以避免cookie问题
                    import time
                    time.sleep(2)  # 避免请求过快

                    response = self.session.get(url, timeout=20, headers=headers)
                    response.encoding = 'utf-8'

                    print(f"  响应状态码: {response.status_code}")
                    if response.status_code == 200:
                        if self.debug:
                            with open(f'debug_etfcom_{symbol}.html', 'w', encoding='utf-8') as f:
                                f.write(response.text)
                            print(f"  调试: ETF.com HTML内容已保存到 debug_etfcom_{symbol}.html")

                        soup = BeautifulSoup(response.text, 'html.parser')
                        holdings = self.parse_etf_com_holdings(soup)
                        if holdings:
                            print(f"  成功解析到 {len(holdings)} 个持仓")
                            return holdings
                        else:
                            print("  未能解析到持仓数据")
                    elif response.status_code == 403:
                        print("  ❌ ETF.com 返回403，可能被反爬虫阻止")
                        continue

                except Exception as e:
                    if self.debug:
                        print(f"  调试: ETF.com URL {url} 失败: {e}")
                    continue

            return None

        except Exception as e:
            print(f"从ETF.com获取数据失败: {e}")
            return None

    def parse_etf_com_holdings(self, soup):
        """解析ETF.com的持仓数据"""
        try:
            holdings = []

            if self.debug:
                print(f"  调试: ETF.com页面标题: {soup.title.string if soup.title else 'N/A'}")

            # 方法1: 查找持仓数据 - ETF.com通常使用特定的结构
            holding_containers = soup.find_all(['div', 'section'], class_=re.compile(r'holding|portfolio|top.*holding', re.I))

            for container in holding_containers:
                if self.debug:
                    print(f"  调试: 找到持仓容器: {container.get('class', [])}")

                # 在容器中查找表格或列表
                tables = container.find_all('table')
                for table in tables:
                    rows = table.find_all('tr')
                    if len(rows) > 1:
                        if self.debug:
                            print(f"  调试: ETF.com表格有 {len(rows)} 行")

                        # 解析表格数据
                        for i, row in enumerate(rows[1:11]):  # 前10行
                            cells = row.find_all(['td', 'th'])
                            if len(cells) >= 2:
                                symbol = ""
                                name = cells[0].get_text().strip()
                                weight = ""

                                # 查找股票代码和权重
                                for j, cell in enumerate(cells):
                                    cell_text = cell.get_text().strip()
                                    if j == 0 and re.match(r'^[A-Z]{2,6}$', cell_text):
                                        symbol = cell_text
                                    elif j == 1 and not symbol:
                                        name = cell_text
                                    elif '%' in cell_text:
                                        weight = cell_text
                                        break

                                if name and name not in ['Total', 'Other', 'Cash', 'Symbol', 'Holding']:
                                    holdings.append({
                                        'rank': i + 1,
                                        'symbol': symbol if symbol else 'N/A',
                                        'name': name,
                                        'weight': weight if weight else '--'
                                    })
                                    if self.debug:
                                        print(f"  调试: ETF.com解析到: {symbol} - {name} ({weight})")

                if holdings:
                    return holdings

            # 方法2: 如果没有找到表格，尝试查找列表结构
            lists = soup.find_all(['ul', 'ol'], class_=re.compile(r'holding|portfolio', re.I))
            for list_elem in lists:
                items = list_elem.find_all('li')
                for i, item in enumerate(items[:10]):
                    text = item.get_text().strip()
                    if '%' in text and len(text) > 5:
                        # 尝试分离名称和权重
                        parts = text.split()
                        weight = next((p for p in parts if '%' in p), '')
                        name = text.replace(weight, '').strip()
                        if name:
                            holdings.append({
                                'rank': i + 1,
                                'symbol': 'N/A',
                                'name': name,
                                'weight': weight
                            })
                            if self.debug:
                                print(f"  调试: ETF.com列表解析到: {name} ({weight})")

                if holdings:
                    return holdings

            # 方法3: 从页面文本中解析
            if not holdings:
                holdings = self.parse_etf_com_text(soup)

            return holdings if holdings else None

        except Exception as e:
            if self.debug:
                print(f"  调试: ETF.com解析出错: {e}")
            return None

    def parse_etf_com_text(self, soup):
        """从ETF.com页面文本中解析持仓数据"""
        try:
            holdings = []
            page_text = soup.get_text()

            if self.debug:
                print(f"  调试: ETF.com页面文本长度: {len(page_text)}")

            # 已知的热门股票代码
            known_stocks = ['NVDA', 'MSFT', 'AAPL', 'AMZN', 'META', 'GOOGL', 'GOOG', 'AVGO', 'TSLA', 'BRK-B', 'ORCL', 'LLY', 'V', 'JPM', 'UNH']

            # 寻找股票代码和权重的模式
            import re
            pattern = r'([A-Z]{2,6})\s+([^0-9\n]+?)\s+(\d+\.\d+%)'
            matches = re.findall(pattern, page_text, re.MULTILINE)

            for match in matches:
                symbol, name, weight = match
                symbol = symbol.strip()
                name = name.strip()
                weight = weight.strip()

                if symbol in known_stocks and len(holdings) < 10:
                    holdings.append({
                        'rank': len(holdings) + 1,
                        'symbol': symbol,
                        'name': name,
                        'weight': weight
                    })
                    if self.debug:
                        print(f"  调试: ETF.com文本解析到: {symbol} - {name} ({weight})")

            return holdings if holdings else None

        except Exception as e:
            if self.debug:
                print(f"  调试: ETF.com文本解析出错: {e}")
            return None

    def get_vanguard_selenium(self, symbol):
        """使用Selenium获取Vanguard持仓数据"""
        try:
            from selenium_etf_scraper import SeleniumETFScraper
            scraper = SeleniumETFScraper(proxy=self.proxy, debug=self.debug)
            holdings = scraper.get_vanguard_holdings_selenium(symbol)
            scraper.close()
            return holdings
        except ImportError:
            if self.debug:
                print("  调试: Selenium未安装，跳过Selenium Vanguard")
            return None
        except Exception as e:
            if self.debug:
                print(f"  调试: Selenium Vanguard失败: {e}")
            return None

    def extract_symbol_from_name(self, company_name):
        """从公司名称中提取股票代码"""
        # 已知的股票代码映射
        symbol_mapping = {
            # ARKK持仓
            'Tesla Inc': 'TSLA',
            'Tesla': 'TSLA',
            'Roku Inc': 'ROKU',
            'Roku': 'ROKU',
            'Coinbase Global Inc': 'COIN',
            'Coinbase': 'COIN',
            'Roblox Corp': 'RBLX',
            'Roblox Corporation': 'RBLX',
            'Roblox': 'RBLX',
            'Tempus AI Inc': 'TEM',
            'Tempus AI': 'TEM',
            'Shopify Inc': 'SHOP',
            'Shopify': 'SHOP',
            'CRISPR Therapeutics AG': 'CRSP',
            'CRISPR Therapeutics': 'CRSP',
            'Robinhood Markets Inc': 'HOOD',
            'Robinhood Markets': 'HOOD',
            'Robinhood': 'HOOD',
            'Palantir Technologies Inc': 'PLTR',
            'Palantir Technologies': 'PLTR',
            'Palantir': 'PLTR',
            'Advanced Micro Devices Inc': 'AMD',
            'Advanced Micro Devices': 'AMD',
            'AMD': 'AMD',

            # QQQ持仓
            'NVIDIA Corp': 'NVDA',
            'NVIDIA Corporation': 'NVDA',
            'NVIDIA': 'NVDA',
            'Microsoft Corp': 'MSFT',
            'Microsoft Corporation': 'MSFT',
            'Microsoft': 'MSFT',
            'Apple Inc': 'AAPL',
            'Apple': 'AAPL',
            'Amazon.com Inc': 'AMZN',
            'Amazon': 'AMZN',
            'Broadcom Inc': 'AVGO',
            'Broadcom': 'AVGO',
            'Meta Platforms Inc': 'META',
            'Meta Platforms': 'META',
            'Meta': 'META',
            'Netflix Inc': 'NFLX',
            'Netflix': 'NFLX',
            'Alphabet Inc': 'GOOGL',  # 默认映射到GOOGL
            'Alphabet': 'GOOGL',
            'Alphabet Inc Class A': 'GOOGL',
            'Alphabet Inc Class C': 'GOOG',
            'Google': 'GOOGL',

            # VEA/IEFA国际股票持仓
            'ASML Holding NV': 'ASML.AS',
            'ASML Holding N.V.': 'ASML.AS',
            'ASML': 'ASML.AS',
            'SAP SE': 'SAP.DE',
            'SAP': 'SAP.DE',
            'AstraZeneca plc': 'AZN.L',
            'AstraZeneca PLC': 'AZN.L',
            'AstraZeneca': 'AZN.L',
            'Nestle SA': 'NESN.SW',
            'Nestlé S.A.': 'NESN.SW',
            'Nestle': 'NESN.SW',
            'Novartis AG': 'NOVN.SW',
            'Novartis AG Registered Shares': 'NOVN.SW',
            'Novartis': 'NOVN.SW',
            'Samsung Electronics Co., Ltd.': '005930.KS',
            'Samsung Electronics Co Ltd': '005930.KS',
            'Samsung Electronics': '005930.KS',
            'Samsung': '005930.KS',
            'Roche Holding AG': 'ROG.SW',
            'Roche Holding': 'ROG.SW',
            'Roche': 'ROG.SW',
            'HSBC Holdings plc': 'HSBA.L',
            'HSBC Holdings PLC': 'HSBA.L',
            'HSBC Holdings': 'HSBA.L',
            'HSBC': 'HSBA.L',
            'Shell plc': 'SHEL.L',
            'Shell PLC': 'SHEL.L',
            'Shell': 'SHEL.L',
            'Royal Bank of Canada': 'RY.TO',
            'Royal Bank': 'RY.TO',
            'Commonwealth Bank of Australia': 'CBA.AX',
            'Commonwealth Bank': 'CBA.AX',
            'Siemens AG': 'SIE.DE',
            'Siemens': 'SIE.DE'
        }

        # 清理公司名称
        clean_name = company_name.strip()

        # 移除常见的后缀
        suffixes_to_remove = [
            ' Ordinary Shares - Class A',
            ' Class A common stock',
            ' Registered Shs -A- Subord Vtg',
            ' Class A',
            ' Inc',
            ' Corp',
            ' Corporation',
            ' Company',
            ' Ltd',
            ' AG',
            ' Ordinary Shares',
            ' common stock'
        ]

        for suffix in suffixes_to_remove:
            if clean_name.endswith(suffix):
                clean_name = clean_name[:-len(suffix)].strip()

        # 直接查找映射
        if clean_name in symbol_mapping:
            return symbol_mapping[clean_name]

        # 尝试部分匹配
        for key, value in symbol_mapping.items():
            if key.lower() in clean_name.lower() or clean_name.lower() in key.lower():
                return value

        # 如果没有找到映射，尝试从名称中提取可能的股票代码
        # 查找全大写的短词（可能是股票代码）
        words = clean_name.split()
        for word in words:
            if word.isupper() and 2 <= len(word) <= 5:
                return word

        # 返回None表示无法提取
        return None

    def get_morningstar_selenium(self, symbol):
        """使用Selenium获取Morningstar持仓数据"""
        try:
            from selenium_etf_scraper import SeleniumETFScraper
            scraper = SeleniumETFScraper(proxy=self.proxy, debug=self.debug)
            holdings = scraper.get_morningstar_holdings_selenium(symbol)
            scraper.close()
            return holdings
        except ImportError:
            if self.debug:
                print("  调试: Selenium未安装，跳过Selenium Morningstar")
            return None
        except Exception as e:
            if self.debug:
                print(f"  调试: Selenium Morningstar失败: {e}")
            return None

    def get_etfcom_selenium(self, symbol):
        """使用Selenium获取ETF.com持仓数据"""
        try:
            from selenium_etf_scraper import SeleniumETFScraper
            scraper = SeleniumETFScraper(proxy=self.proxy, debug=self.debug)
            holdings = scraper.get_etfcom_holdings_selenium(symbol)
            scraper.close()
            return holdings
        except ImportError:
            if self.debug:
                print("  调试: Selenium未安装，跳过Selenium ETF.com")
            return None
        except Exception as e:
            if self.debug:
                print(f"  调试: Selenium ETF.com失败: {e}")
            return None

    def get_simple_etf_info(self, symbol):
        """获取简单的ETF信息（备用方法）"""
        try:
            # 尝试从多个简单的API获取基本信息
            urls = [
                f"https://query1.finance.yahoo.com/v8/finance/chart/{symbol}",
                f"https://finance.yahoo.com/quote/{symbol}",
            ]

            for url in urls:
                try:
                    print(f"  尝试简单API: {url}")
                    response = self.session.get(url, timeout=10)

                    if response.status_code == 200:
                        # 如果是JSON响应
                        if 'application/json' in response.headers.get('content-type', ''):
                            response.json()  # 验证是否为有效JSON
                            # 这里可以解析JSON数据，但通常不包含持仓信息
                            print(f"  获取到JSON数据，但可能不包含持仓信息")
                        else:
                            # HTML响应，尝试简单解析
                            soup = BeautifulSoup(response.text, 'html.parser')
                            title = soup.find('title')
                            if title and symbol in title.get_text():
                                print(f"  确认ETF {symbol} 存在，但无法获取持仓详情")
                                return [{'rank': 1, 'symbol': 'N/A', 'name': f'{symbol} ETF持仓信息暂时无法获取', 'weight': 'N/A'}]
                except:
                    continue

            return None

        except Exception as e:
            print(f"简单ETF信息获取失败: {e}")
            return None

    def get_yfinance_holdings(self, symbol):
        """使用yfinance库获取ETF持仓数据"""
        if not YFINANCE_AVAILABLE:
            print(f"  yfinance库未安装，跳过此数据源")
            return None

        try:
            print(f"  尝试使用yfinance获取 {symbol} 数据...")

            # 创建yfinance对象
            etf = yf.Ticker(symbol)

            # 获取基本信息
            info = etf.info
            if not info or 'symbol' not in info:
                print(f"  yfinance未找到 {symbol} 的信息")
                return None

            # 方法1: 尝试yfinance内置的holdings方法
            try:
                if hasattr(etf, 'get_holdings'):
                    holdings_data = etf.get_holdings()
                    if holdings_data is not None and not holdings_data.empty:
                        holdings = []
                        for i, (_, row) in enumerate(holdings_data.head(10).iterrows()):
                            holdings.append({
                                'rank': i + 1,
                                'symbol': row.get('Symbol', 'N/A'),
                                'name': row.get('Holding', row.get('Name', 'N/A')),
                                'weight': f"{row.get('% Assets', 0):.2f}%" if 'Assets' in str(row.get('% Assets', '')) else 'N/A'
                            })

                        if holdings:
                            print(f"  ✅ yfinance内置方法成功获取到 {len(holdings)} 个持仓")
                            return holdings
            except Exception as e:
                if self.debug:
                    print(f"  调试: yfinance内置方法失败: {e}")

            # 方法2: 使用yfinance + 网络爬取Yahoo Finance
            try:
                etf_name = info.get('longName', info.get('shortName', f'{symbol} ETF'))
                print(f"  yfinance确认ETF存在: {etf_name}，尝试爬取Yahoo Finance持仓数据...")

                # 直接调用Yahoo Finance爬取方法
                yahoo_holdings = self.get_us_etf_holdings_yahoo(symbol)
                if yahoo_holdings:
                    print(f"  ✅ yfinance+Yahoo爬取成功获取到 {len(yahoo_holdings)} 个持仓")
                    return yahoo_holdings

            except Exception as e:
                if self.debug:
                    print(f"  调试: yfinance+Yahoo爬取失败: {e}")

            # 如果都失败了，记录ETF信息但不返回，继续尝试其他数据源
            etf_name = info.get('longName', info.get('shortName', f'{symbol} ETF'))
            print(f"  yfinance确认ETF存在: {etf_name}，但无法获取详细持仓，继续尝试其他数据源")
            return None

        except Exception as e:
            print(f"  yfinance获取失败: {e}")
            return None

    def get_vanguard_csv_holdings(self, symbol):
        """尝试从Vanguard下载CSV持仓文件"""
        try:
            # Vanguard的CSV下载链接模式（需要根据实际情况调整）
            csv_urls = [
                f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol}/portfolio-holdings/csv",
                f"https://investor.vanguard.com/rs/gre/gra/1.3.0/documents/{symbol}_holdings.csv",
            ]

            for url in csv_urls:
                try:
                    print(f"  尝试下载Vanguard CSV: {url}")
                    response = self.session.get(url, timeout=15)

                    if response.status_code == 200 and 'text/csv' in response.headers.get('content-type', ''):
                        # 解析CSV数据
                        from io import StringIO
                        csv_data = StringIO(response.text)
                        df = pd.read_csv(csv_data)

                        if not df.empty and len(df) > 0:
                            holdings = []
                            for i, row in df.head(10).iterrows():
                                # 根据CSV列名调整（Vanguard的列名可能不同）
                                name = row.get('Holding Name', row.get('Security Name', row.get('Name', 'N/A')))
                                weight = row.get('% of Net Assets', row.get('Weight', row.get('Percentage', 'N/A')))
                                symbol_col = row.get('Ticker', row.get('Symbol', 'N/A'))

                                if str(name) != 'N/A' and str(name) != 'nan':
                                    holdings.append({
                                        'rank': i + 1,
                                        'symbol': str(symbol_col) if str(symbol_col) != 'nan' else 'N/A',
                                        'name': str(name),
                                        'weight': f"{weight}%" if isinstance(weight, (int, float)) else str(weight)
                                    })

                            if holdings:
                                print(f"  成功从Vanguard CSV获取到 {len(holdings)} 个持仓")
                                return holdings
                except Exception as e:
                    print(f"  CSV下载失败: {e}")
                    continue

            return None

        except Exception as e:
            print(f"Vanguard CSV获取失败: {e}")
            return None

    def show_data_source_menu(self):
        """显示数据源选择菜单"""
        print("\n📊 可用数据源:")
        print("1. yfinance库 (快速，基础验证)")
        print("2. Vanguard Selenium (官方数据，最准确，较慢)")
        print("3. Yahoo Finance (稳定可靠)")
        print("4. Vanguard CSV (官方CSV数据)")
        print("5. Vanguard官网 (官方网站)")
        print("6. ETF.com (第三方数据)")
        print("7. Morningstar (专业数据)")
        print("8. Selenium Morningstar (Selenium版本)")
        print("9. Selenium ETF.com (Selenium版本)")
        print("10. Vanguard API (官方API)")
        print("11. 简单API (备用数据源)")
        print("0. 自动模式 (按优先级尝试所有数据源)")
        print("-" * 50)

        while True:
            try:
                choice = input("请选择数据源 (0-11): ").strip()
                if choice in ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11']:
                    return int(choice)
                else:
                    print("❌ 请输入有效的选项 (0-11)")
            except KeyboardInterrupt:
                print("\n\n👋 用户取消操作")
                return None
            except:
                print("❌ 请输入有效的数字")

    def get_us_etf_holdings(self, symbol, source_choice=None):
        """获取美股ETF持仓数据，支持数据源选择"""
        symbol = symbol.upper()

        # 数据源列表，按优先级排序
        data_sources = [
            ("yfinance库", self.get_yfinance_holdings),
            ("Vanguard Selenium", self.get_vanguard_selenium_final),
            ("Yahoo Finance", self.get_us_etf_holdings_yahoo),
            ("Vanguard CSV", self.get_vanguard_csv_holdings),
            ("Vanguard官网", self.get_vanguard_holdings),
            ("ETF.com", self.get_etf_com_holdings),
            ("Morningstar", self.get_us_etf_holdings_morningstar),
            ("Selenium Morningstar", self.get_morningstar_selenium),
            ("Selenium ETF.com", self.get_etfcom_selenium),
            ("Vanguard API", self.get_vanguard_api_holdings),
            ("简单API", self.get_simple_etf_info)
        ]

        # 如果指定了数据源选择
        if source_choice is not None and 1 <= source_choice <= len(data_sources):
            source_name, get_method = data_sources[source_choice - 1]
            print(f"  使用指定数据源: {source_name}")
            try:
                holdings = get_method(symbol)
                if holdings and len(holdings) > 0:
                    print(f"  ✅ 从 {source_name} 成功获取到 {len(holdings)} 个持仓")
                    return holdings
                else:
                    print(f"  ❌ {source_name} 未获取到数据")
                    return None
            except Exception as e:
                print(f"  ❌ {source_name} 获取失败: {e}")
                if self.debug:
                    traceback.print_exc()
                return None

        # 自动模式：依次尝试各个数据源
        print(f"  开始从多个数据源获取 {symbol} 的持仓数据...")
        for source_name, get_method in data_sources:
            try:
                print(f"  尝试数据源: {source_name}")
                holdings = get_method(symbol)
                if holdings and len(holdings) > 0:
                    print(f"  ✅ 从 {source_name} 成功获取到 {len(holdings)} 个持仓")
                    return holdings
                else:
                    print(f"  ❌ {source_name} 未获取到数据")
                    # 在尝试下一个数据源前稍作延迟
                    time.sleep(1)
            except Exception as e:
                print(f"  ❌ {source_name} 获取失败: {e}")
                time.sleep(1)
                continue

        print(f"  ❌ 所有数据源都无法获取 {symbol} 的持仓数据")

        # 最后尝试：至少确认ETF存在
        try:
            etf_name = self.get_us_etf_name(symbol)
            if etf_name and etf_name != f"US ETF ({symbol})":
                print(f"  ℹ️ 最终确认: {etf_name} 存在，但无法获取持仓详情")
                return [{'rank': 1, 'symbol': 'N/A', 'name': f'{etf_name} - 所有数据源均无法获取持仓详情', 'weight': 'N/A'}]
        except:
            pass

        return None

    def get_us_etf_name(self, symbol):
        """获取美股ETF名称"""
        try:
            url = f"https://finance.yahoo.com/quote/{symbol}"
            response = self.session.get(url, timeout=10)
            response.encoding = 'utf-8'

            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 查找ETF名称
                title_elements = soup.find_all(['h1', 'title'])
                for element in title_elements:
                    text = element.get_text().strip()
                    if symbol in text and ('ETF' in text or 'Fund' in text):
                        # 提取名称部分
                        name_match = re.search(r'([^(（]+(?:ETF|Fund))', text)
                        if name_match:
                            return name_match.group(1).strip()
                        
                        # 如果没有匹配到，返回完整标题的前半部分
                        parts = text.split(' - ')
                        if len(parts) > 0:
                            return parts[0].strip()
        except:
            pass

        return f"US ETF ({symbol})"

    def get_vanguard_api_holdings(self, symbol):
        """尝试从Vanguard API获取持仓数据"""
        try:
            # 尝试多个可能的API端点
            api_urls = [
                f"https://api.vanguard.com/rs/gre/gra/1.7.0/datasets/{symbol.upper()}/portfolio-holdings",
                f"https://investor.vanguard.com/rs/gre/gra/1.7.0/datasets/{symbol.upper()}/portfolio-holdings",
                f"https://investor.vanguard.com/api/etf/{symbol.lower()}/holdings",
                f"https://investor.vanguard.com/investment-products/etfs/profile/{symbol.lower()}/portfolio-holdings/csv"
            ]

            for api_url in api_urls:
                try:
                    if self.debug:
                        print(f"  调试: 尝试Vanguard API: {api_url}")

                    response = self.session.get(api_url, timeout=15)
                    if response.status_code == 200:
                        # 尝试解析JSON
                        try:
                            data = response.json()
                            holdings = self.parse_vanguard_api_json(data, symbol)
                            if holdings:
                                return holdings
                        except:
                            # 尝试解析CSV
                            holdings = self.parse_vanguard_csv_content(response.text, symbol)
                            if holdings:
                                return holdings

                except Exception as e:
                    if self.debug:
                        print(f"  调试: API请求失败: {e}")
                    continue

        except Exception as e:
            if self.debug:
                print(f"  调试: Vanguard API获取失败: {e}")

        return None

    def parse_vanguard_api_json(self, data, symbol):
        """解析Vanguard API JSON数据"""
        holdings = []
        try:
            # 尝试多种可能的JSON结构
            possible_paths = [
                ['fund', 'portfolio', 'holdings'],
                ['portfolio', 'holdings'],
                ['holdings'],
                ['data', 'holdings'],
                ['fundProfile', 'holdings']
            ]

            holdings_data = None
            for path in possible_paths:
                temp_data = data
                try:
                    for key in path:
                        temp_data = temp_data[key]
                    holdings_data = temp_data
                    break
                except (KeyError, TypeError):
                    continue

            if holdings_data and isinstance(holdings_data, list):
                for i, holding in enumerate(holdings_data[:10]):
                    if isinstance(holding, dict):
                        symbol_key = holding.get('ticker', holding.get('symbol', holding.get('code', '')))
                        name_key = holding.get('name', holding.get('description', holding.get('companyName', '')))
                        weight_key = holding.get('weight', holding.get('percentage', holding.get('allocation', 0)))

                        if symbol_key and name_key:
                            holdings.append({
                                'rank': i + 1,
                                'symbol': str(symbol_key).strip(),
                                'name': str(name_key).strip(),
                                'weight': f"{float(weight_key):.2f}%" if isinstance(weight_key, (int, float)) else str(weight_key)
                            })

        except Exception as e:
            if self.debug:
                print(f"  调试: Vanguard API JSON解析失败: {e}")

        return holdings if len(holdings) > 0 else None

    def format_holdings_text(self, holdings):
        """格式化持仓数据为文本（只显示股票代码）"""
        if not holdings:
            return "暂无数据"

        result = []
        for holding in holdings:
            symbol = holding.get('symbol', '').strip()
            weight = holding.get('weight', '').strip()

            # 验证并清理股票代码
            validated_symbol = self.extract_and_validate_symbol(
                symbol, holding.get('name', '')
            )

            if validated_symbol:
                if weight and weight != '--':
                    result.append(f"{validated_symbol}({weight})")
                else:
                    result.append(validated_symbol)
            elif symbol and symbol != 'N/A':
                # 如果无法验证，仍然显示原始代码但加上警告
                if weight and weight != '--':
                    result.append(f"{symbol}({weight})⚠️")
                else:
                    result.append(f"{symbol}⚠️")

        return "、".join(result)

    def query_single_etf(self, symbol, source_choice=None):
        """查询单个美股ETF"""
        print(f"正在查询美股ETF {symbol}...")

        # 获取ETF名称和持仓
        etf_name = self.get_us_etf_name(symbol)
        holdings = self.get_us_etf_holdings(symbol, source_choice)
        
        if holdings:
            print(f"\n✅ {etf_name} 前十大持仓:")
            print("-" * 50)
            for holding in holdings:
                symbol_str = holding.get('symbol', 'N/A')
                weight_str = holding.get('weight', '')

                # 验证并清理股票代码
                validated_symbol = self.extract_and_validate_symbol(
                    symbol_str, holding.get('name', '')
                )

                if validated_symbol:
                    print(f"{holding['rank']:2d}. {validated_symbol:<8} {weight_str}")
                else:
                    # 如果股票代码无效，显示警告
                    print(f"{holding['rank']:2d}. {symbol_str:<8} {weight_str} ⚠️ 代码可疑")
            print("-" * 50)
            
            # 保存到文件
            filename = f"us_etf_{symbol}_holdings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            holdings_text = self.format_holdings_text(holdings)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"美股ETF代码: {symbol}\n")
                f.write(f"ETF名称: {etf_name}\n")
                f.write(f"十大持仓: {holdings_text}\n")
                f.write(f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            print(f"✅ 数据已保存到: {filename}")
            return True
        else:
            print(f"❌ 无法获取美股ETF {symbol} 的持仓数据")
            return False

    def process_excel_file(self, excel_file, sheet_names=None, source_choice=None):
        """处理Excel/CSV文件中的美股ETF列表"""
        try:
            file_extension = excel_file.lower().split('.')[-1]

            if file_extension == 'csv':
                print(f"正在处理CSV文件: {excel_file}")
                # 读取CSV文件
                df = pd.read_csv(excel_file, encoding='utf-8')
                # 将CSV数据包装成类似Excel的格式
                all_sheets = {'Sheet1': df}
            else:
                print(f"正在处理Excel文件: {excel_file}")
                # 读取Excel文件
                if sheet_names:
                    print(f"指定工作表: {', '.join(sheet_names)}")
                    all_sheets = pd.read_excel(excel_file, sheet_name=sheet_names, engine='openpyxl')
                else:
                    all_sheets = pd.read_excel(excel_file, sheet_name=None, engine='openpyxl')
                print(f"发现工作表: {', '.join(all_sheets.keys())}")

            # 如果只有一个工作表，转换为字典格式
            if isinstance(all_sheets, pd.DataFrame):
                all_sheets = {'Sheet1': all_sheets}

            total_processed = 0
            total_success = 0
            results = {}

            # 处理每个工作表
            for sheet_name, df in all_sheets.items():
                print(f"\n📋 处理工作表: {sheet_name}")

                # 提取美股ETF代码
                etf_symbols = []

                # 首先尝试从"代码"列直接获取ETF代码
                if '代码' in df.columns:
                    for row_idx, row in df.iterrows():
                        value = str(row['代码']).strip().upper()
                        if value and value != 'NAN' and re.match(r'^[A-Z]{1,5}$', value) and self.is_valid_us_etf_symbol(value):
                            if value not in etf_symbols:
                                etf_symbols.append(value)
                                if self.debug:
                                    print(f"  调试: 从'代码'列发现ETF代码 {value} 在第{row_idx+1}行")

                # 如果没有找到足够的代码，再扫描所有列
                if len(etf_symbols) < 5:
                    for row_idx, row in df.iterrows():
                        for col in df.columns:
                            if col == '代码':  # 跳过已经处理过的代码列
                                continue
                            value = str(row[col]).strip().upper()
                            # 美股ETF代码通常是1-5位字母，排除常见的非ETF词汇
                            if (re.match(r'^[A-Z]{1,5}$', value) and
                                self.is_valid_us_etf_symbol(value) and
                                value not in ['NAN', 'NULL', 'NONE', '宽基', '科技', '行业']):
                                if value not in etf_symbols:
                                    etf_symbols.append(value)
                                    if self.debug:
                                        print(f"  调试: 发现ETF代码 {value} 在第{row_idx+1}行，列'{col}'")
                            elif self.debug and re.match(r'^[A-Z]{1,5}$', value) and value not in ['NAN', 'NULL']:
                                print(f"  调试: 跳过可疑代码 {value} 在第{row_idx+1}行，列'{col}'")

                print(f"找到 {len(etf_symbols)} 个美股ETF代码")

                # 处理每个ETF
                sheet_results = {}
                for i, symbol in enumerate(etf_symbols, 1):
                    print(f"[{i}/{len(etf_symbols)}] 处理 {symbol}...")

                    etf_name = self.get_us_etf_name(symbol)
                    holdings = self.get_us_etf_holdings(symbol, source_choice)

                    if holdings:
                        holdings_text = self.format_holdings_text(holdings)
                        sheet_results[symbol] = {
                            'name': etf_name,
                            'holdings': holdings_text,
                            'status': 'success'
                        }
                        total_success += 1
                        print(f"  ✅ 成功")
                    else:
                        sheet_results[symbol] = {
                            'name': etf_name,
                            'holdings': '获取失败',
                            'status': 'failed'
                        }
                        print(f"  ❌ 失败")

                    total_processed += 1

                    # 避免请求过快
                    if i < len(etf_symbols):
                        time.sleep(3)  # 美股数据源可能需要更长间隔

                results[sheet_name] = sheet_results

            # 生成结果文件
            self.save_results(excel_file, all_sheets, results)

            print(f"\n🎉 处理完成!")
            print(f"总计: {total_processed} 个美股ETF")
            print(f"成功: {total_success} 个")
            print(f"失败: {total_processed - total_success} 个")
            if total_processed > 0:
                print(f"成功率: {total_success/total_processed*100:.1f}%")

            return True

        except Exception as e:
            print(f"❌ 处理Excel文件失败: {e}")
            traceback.print_exc()
            return False

    def save_results(self, original_file, all_sheets, results):
        """保存处理结果"""
        try:
            base_name = os.path.splitext(original_file)[0]
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_extension = original_file.lower().split('.')[-1]

            # 根据原文件类型决定输出格式
            if file_extension == 'csv':
                # 处理CSV文件
                output_file = f"{base_name}_updated.csv"

                # 只有一个"工作表"（Sheet1）
                if 'Sheet1' in all_sheets and 'Sheet1' in results:
                    df = all_sheets['Sheet1'].copy()
                    sheet_results = results['Sheet1']

                    # 添加十大持仓列（如果不存在）
                    if '十大持仓' not in df.columns:
                        df['十大持仓'] = ''

                    # 更新持仓数据
                    # 确保'十大持仓'列为object类型以避免警告
                    df['十大持仓'] = df['十大持仓'].astype('object')

                    for idx, row in df.iterrows():
                        for col in df.columns:
                            value = str(row[col]).strip().upper()
                            if re.match(r'^[A-Z]{1,5}$', value) and value in sheet_results:
                                df.loc[idx, '十大持仓'] = sheet_results[value]['holdings']
                                break

                    # 保存为CSV
                    df.to_csv(output_file, index=False, encoding='utf-8-sig')
                    print(f"✅ CSV文件已更新: {output_file}")
                else:
                    print("❌ CSV文件处理失败：未找到有效数据")
            else:
                # 处理Excel文件
                output_excel = f"{base_name}_updated.xlsx"

                with pd.ExcelWriter(output_excel, engine='openpyxl') as writer:
                    for sheet_name, df in all_sheets.items():
                        # 复制原数据
                        updated_df = df.copy()

                        # 添加十大持仓列（如果不存在）
                        if '十大持仓' not in updated_df.columns:
                            updated_df['十大持仓'] = ''

                        # 更新持仓数据
                        if sheet_name in results:
                            sheet_results = results[sheet_name]
                            for idx, row in updated_df.iterrows():
                                for col in updated_df.columns:
                                    value = str(row[col]).strip().upper()
                                    if re.match(r'^[A-Z]{1,5}$', value) and value in sheet_results:
                                        # 确保'十大持仓'列存在且为object类型
                                        if '十大持仓' not in updated_df.columns:
                                            updated_df['十大持仓'] = ''
                                        updated_df['十大持仓'] = updated_df['十大持仓'].astype('object')
                                        updated_df.loc[idx, '十大持仓'] = sheet_results[value]['holdings']
                                        break

                        # 写入工作表
                        updated_df.to_excel(writer, sheet_name=sheet_name, index=False)

                print(f"✅ Excel文件已更新: {output_excel}")

            # 生成CSV汇总文件
            csv_data = []
            for sheet_name, sheet_results in results.items():
                for symbol, data in sheet_results.items():
                    csv_data.append({
                        '工作表': sheet_name,
                        'ETF代码': symbol,
                        'ETF名称': data['name'],
                        '十大持仓': data['holdings'],
                        '状态': data['status'],
                        '更新时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })

            csv_file = f"{base_name}_summary_{timestamp}.csv"
            pd.DataFrame(csv_data).to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"✅ CSV汇总文件已生成: {csv_file}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


def main():
    """主函数"""
    # 检查调试模式
    debug = '--debug' in sys.argv
    if debug:
        sys.argv.remove('--debug')
        print("🔍 调试模式已启用")

    # 检查数据源选择
    source_choice = None
    if '--source' in sys.argv:
        source_index = sys.argv.index('--source')
        if source_index + 1 < len(sys.argv):
            try:
                source_choice = int(sys.argv[source_index + 1])
                # 移除数据源参数
                sys.argv.pop(source_index)  # 移除 --source
                sys.argv.pop(source_index)  # 移除数据源编号
                print(f"🎯 指定数据源: {source_choice}")
            except ValueError:
                print("❌ 数据源编号必须是数字")
                return

    # 检查是否需要使用代理
    proxy = None
    if '--proxy' in sys.argv:
        proxy_index = sys.argv.index('--proxy')
        if proxy_index + 1 < len(sys.argv):
            proxy = sys.argv[proxy_index + 1]
            # 移除代理参数，避免影响其他参数解析
            sys.argv.pop(proxy_index)  # 移除 --proxy
            sys.argv.pop(proxy_index)  # 移除代理地址

    # 自动检测常见代理设置
    if not proxy:
        common_proxies = [
            'http://127.0.0.1:8001',  # 你的代理设置
            'http://127.0.0.1:1081',  # 常见Socks代理
            'http://127.0.0.1:7890',  # Clash代理
            'http://127.0.0.1:8080',  # 常见HTTP代理
        ]

        for test_proxy in common_proxies:
            try:
                # 快速测试代理是否可用
                response = requests.get('https://httpbin.org/ip',
                                      proxies={'http': test_proxy, 'https': test_proxy},
                                      timeout=3)
                if response.status_code == 200:
                    proxy = test_proxy
                    print(f"🌐 自动检测到可用代理: {proxy}")
                    break
            except:
                continue

    analyzer = USETFAnalyzer(proxy=proxy, debug=debug)

    print("🎯 美股ETF持仓分析工具")
    print("=" * 50)

    # 显示帮助信息
    if '--help' in sys.argv or '-h' in sys.argv:
        print("使用方法:")
        print("  python3 us_etf_analyzer.py                    # 交互模式")
        print("  python3 us_etf_analyzer.py SPY                # 查询单个ETF")
        print("  python3 us_etf_analyzer.py SPY --debug        # 调试模式")
        print("  python3 us_etf_analyzer.py SPY --source 2     # 指定数据源")
        print("  python3 us_etf_analyzer.py file.xlsx          # 处理Excel文件")
        print("  python3 us_etf_analyzer.py file.csv           # 处理CSV文件")
        print("")
        print("数据源编号:")
        print("  1. yfinance库 (快速，基础验证)")
        print("  2. Vanguard Selenium (官方数据，最准确，较慢)")
        print("  3. Yahoo Finance (稳定可靠)")
        print("  4. Vanguard CSV (官方CSV数据)")
        print("  5. Vanguard官网 (官方网站)")
        print("  6. ETF.com (第三方数据)")
        print("  7. Morningstar (专业数据)")
        print("  8. Selenium Morningstar (Selenium版本)")
        print("  9. Selenium ETF.com (Selenium版本)")
        print("  10. Vanguard API (官方API)")
        print("  11. 简单API (备用数据源)")
        print("  0. 自动模式 (按优先级尝试所有数据源，默认)")
        return

    if len(sys.argv) == 1:
        # 交互模式
        print("请选择功能:")
        print("1. 查询单个美股ETF")
        print("2. 处理Excel文件")
        choice = input("请输入选择 (1/2): ").strip()

        if choice == '1':
            symbol = input("请输入美股ETF代码 (如 SPY, QQQ): ").strip().upper()
            if symbol:
                # 询问是否选择数据源
                print("\n是否要选择特定数据源？")
                print("y - 选择数据源")
                print("n - 使用自动模式 (推荐)")
                source_choice_input = input("请选择 (y/n): ").strip().lower()

                source_choice = None
                if source_choice_input == 'y':
                    source_choice = analyzer.show_data_source_menu()
                    if source_choice is None:
                        return

                analyzer.query_single_etf(symbol, source_choice)
        elif choice == '2':
            file_path = input("请输入Excel/CSV文件路径: ").strip()
            if file_path and os.path.exists(file_path):
                analyzer.process_excel_file(file_path, source_choice=source_choice)
            else:
                print("❌ 文件不存在")
        else:
            print("❌ 无效选择")

    elif len(sys.argv) == 2:
        arg = sys.argv[1]
        if re.match(r'^[A-Za-z]{1,5}$', arg):
            # 单个美股ETF代码
            analyzer.query_single_etf(arg.upper(), source_choice)
        elif os.path.exists(arg):
            # Excel/CSV文件
            analyzer.process_excel_file(arg, source_choice=source_choice)
        else:
            print(f"❌ 无效参数: {arg}")

    elif len(sys.argv) >= 3:
        excel_file = sys.argv[1]
        sheet_names = sys.argv[2:]
        if os.path.exists(excel_file):
            analyzer.process_excel_file(excel_file, sheet_names, source_choice)
        else:
            print(f"❌ 文件不存在: {excel_file}")


if __name__ == "__main__":
    main()
